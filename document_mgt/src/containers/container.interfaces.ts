import CustomerControllers from '../users/regular/controllers/customer.controller';
import DocumentController from '../users/regular/controllers/documents.controller';
import InvoiceController from '../users/regular/controllers/invoice.controller';
import ProductControllers from '../users/regular/controllers/product.controller';
import ItemControllers from '../users/regular/controllers/item.controller';
import ServiceControllers from '../users/regular/controllers/service.controller';
import StatControllers from '../users/regular/controllers/stats.controller';
import UtilitiesControllers from '../utilities.controller';
import { AuthMiddleware } from '../middlewares/auth/auth.middleware';
import ImageUploader from '../middlewares/utils/image-upload.utilities';
import CustomerServices from '../users/regular/services/customer.service';
import CreditNoteServices from '../users/regular/services/credit-note.service';
import DocumentServices from '../users/regular/services/documents.service';
import InvoiceServices from '../users/regular/services/invoice.service';
import ReceiptServices from '../users/regular/services/receipt.service';
import productServices from '../users/regular/services/product.service';
import ItemServices from '../users/regular/services/items.service';
import ServiceServices from '../users/regular/services/service.service';
import StatServices from '../users/regular/services/stats.service';
import ProductCategoryServices from '../users/regular/services/category.service';
import ProductCategoryControllers from '../users/regular/controllers/product-category.controller';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';
import AdminDocumentControllers from '../users/admin/controllers/document.controller';
import AdminDocumentServices from '../users/admin/services/document.admin.service';

export interface ContainerInstanceTypes {
  receiptServices: ReceiptServices;
  creditNoteServices: CreditNoteServices;
  customerServices: CustomerServices;
  itemServices: ItemServices;
  serviceServices: ServiceServices;
  invoiceServices: InvoiceServices;
  documentService: DocumentServices;
  statServices: StatServices;
  documentControllers: DocumentController;
  invoiceControllers: InvoiceController;
  customerControllers: CustomerControllers;
  itemControllers: ItemControllers;
  serviceControllers: ServiceControllers;
  statControllers: StatControllers;
  authMiddleware: AuthMiddleware;
  utilitiesControllers: UtilitiesControllers;
  productServices: productServices;
  productControllers: ProductControllers;
  productCategoryServices: ProductCategoryServices;
  productCategoryControllers: ProductCategoryControllers;
  imageUploader: ImageUploader;
  backgroundTaskManagers: BackgroundTaskManager;
  adminDocumentServices: AdminDocumentServices;
  adminDocumentControllers: AdminDocumentControllers;
}
