import { ContainerKeys } from './container.types';
import CustomerControllers from '../users/regular/controllers/customer.controller';
import DocumentController from '../users/regular/controllers/documents.controller';
import InvoiceController from '../users/regular/controllers/invoice.controller';
import ItemControllers from '../users/regular/controllers/item.controller';
import StatControllers from '../users/regular/controllers/stats.controller';
import CustomerServices from '../users/regular/services/customer.service';
import ItemServices from '../users/regular/services/items.service';
import StatServices from '../users/regular/services/stats.service';
import CreditNoteServices from '../users/regular/services/credit-note.service';
import DocumentServices from '../users/regular/services/documents.service';
import InvoiceServices from '../users/regular/services/invoice.service';
import ReceiptServices from '../users/regular/services/receipt.service';
import ServiceServices from '../users/regular/services/service.service';
import { ContainerInstanceTypes } from './container.interfaces';
import ServiceControllers from '../users/regular/controllers/service.controller';
import { AuthMiddleware } from '../middlewares/auth/auth.middleware';
import UtilitiesControllers from '../utilities.controller';
import productServices from '../users/regular/services/product.service';
import ProductControllers from '../users/regular/controllers/product.controller';
import ImageUploader from '../middlewares/utils/image-upload.utilities';
import ProductCategoryServices from '../users/regular/services/category.service';
import ProductCategoryControllers from '../users/regular/controllers/product-category.controller';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';
import AdminDocumentControllers from '../users/admin/controllers/document.controller';
import AdminDocumentServices from '../users/admin/services/document.admin.service';
import Document from '../models/documents.model';

class Container {
  private instances: Partial<Record<ContainerKeys, ContainerInstanceTypes[ContainerKeys]>> = {};

  register<K extends ContainerKeys>(key: K, instance: ContainerInstanceTypes[K]): void {
    this.instances[key] = instance;
  }

  resolve<K extends ContainerKeys>(key: K): ContainerInstanceTypes[K] {
    return this.instances[key] as ContainerInstanceTypes[K];
  }
}

const container = new Container();

container.register('utilitiesControllers', new UtilitiesControllers());
container.register('authMiddleware', new AuthMiddleware());
container.register('receiptServices', new ReceiptServices());
container.register('creditNoteServices', new CreditNoteServices());

container.register('customerServices', new CustomerServices());
container.register('itemServices', new ItemServices());
container.register('serviceServices', new ServiceServices());

container.register(
  'invoiceServices',
  new InvoiceServices(
    container.resolve('itemServices'),
    container.resolve('serviceServices'),
    container.resolve('customerServices')
  )
);

container.register(
  'documentService',
  new DocumentServices(
    container.resolve('invoiceServices'),
    container.resolve('receiptServices'),
    container.resolve('creditNoteServices'),
    container.resolve('itemServices'),
    container.resolve('customerServices'),
    container.resolve('serviceServices')
  )
);

container.register('statServices', new StatServices());

container.register(
  'documentControllers',
  new DocumentController(
    container.resolve('documentService'),
    container.resolve('itemServices'),
    container.resolve('serviceServices'),
    container.resolve('customerServices')
  )
);

container.register(
  'invoiceControllers',
  new InvoiceController(container.resolve('invoiceServices'))
);

container.register(
  'customerControllers',
  new CustomerControllers(container.resolve('customerServices'))
);

container.register('itemControllers', new ItemControllers(container.resolve('itemServices')));
container.register(
  'serviceControllers',
  new ServiceControllers(container.resolve('serviceServices'))
);
container.register('statControllers', new StatControllers(container.resolve('statServices')));

container.register('productCategoryServices', new ProductCategoryServices());

container.register(
  'productServices',
  new productServices(container.resolve('productCategoryServices'))
);
container.register(
  'productControllers',
  new ProductControllers(container.resolve('productServices'))
);

container.register(
  'productCategoryControllers',
  new ProductCategoryControllers(container.resolve('productCategoryServices'))
);

container.register('imageUploader', new ImageUploader());

container.register('backgroundTaskManagers', new BackgroundTaskManager());

container.register('adminDocumentServices', new AdminDocumentServices(Document));

container.register(
  'adminDocumentControllers',
  new AdminDocumentControllers(container.resolve('adminDocumentServices'))
);

export default container;
