import { Application } from 'express';
import { captureAppDetails } from '../middlewares/utils/utils.middleware';
import AdminAccessRouter from '../users/admin/routes/index.routes';
import SystemAccessRouter from './system/index.routes';
import { API_VERSION } from '../constants/values.constants';
import UtilitiesRouter from './utilities.routes';
import container from '../containers/container.global';
import { RateLimiters } from '../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../utilities/guards';
import OrgDocumentRouter from '../users/regular/routes/documents/index.routes';
import OrgInventoryRouter from '../users/regular/routes/inventories/index.routes';

export const indexRoutes = (app: Application) => {
  app.use(captureAppDetails);

  if (isProductionEnv) {
    app.use(RateLimiters.global);
  }

  app.use(`${API_VERSION}/system`, SystemAccessRouter);
  app.use(`${API_VERSION}/documents`, UtilitiesRouter);
  app.use(`${API_VERSION}/documents`, OrgDocumentRouter);
  app.use(`${API_VERSION}/inventories`, OrgInventoryRouter);
  app.use(`${API_VERSION}/admin`, AdminAccessRouter);

  app.all('*', container.resolve('utilitiesControllers').resourceNotFound);
};
