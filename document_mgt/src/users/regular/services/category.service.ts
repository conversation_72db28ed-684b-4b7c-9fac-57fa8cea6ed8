import { FindOptions, Op, WhereOptions } from 'sequelize';
import { Errors } from '../../../constants/errors.constants';
import { CreateProductCategoryPayload } from '../../../interfaces/request-body/inventory-payload.interfaces';
import { ConflictError } from '../../../middlewares/error_handlers/app-error';
import ProductCategory, { ProductCategoryAttributes } from '../../../models/product-category.model';
import BaseServices from '../../../base.service';
import sequelize from '../../../config/database/connection';

interface SkippedCategory extends CreateProductCategoryPayload {
  reason: string;
}
export default class ProductCategoryServices extends BaseServices<ProductCategory> {
  constructor() {
    super(ProductCategory);
  }

  async getOneCategory(orgId: string, categoryId: string) {
    return await this.getOne({ organization_id: orgId, id: categoryId }, {}, true);
  }

  async getAllCategories(orgId: string, offset?: number, limit?: number) {
    return await this.getManyAndCount({ organization_id: orgId }, { offset, limit }, true);
  }

  async createACategory(orgId: string, payload: CreateProductCategoryPayload) {
    const existingCategories = await this.getManyAndCount({
      organization_id: orgId,
      name: payload.name,
    });

    if (existingCategories.count > 0) {
      throw new ConflictError(Errors.CATEGORY_EXISTS);
    }

    payload['organization_id'] = orgId;
    return await this.create(payload);
  }

  async updateACategory(
    orgId: string,
    categoryId: string,
    updates: Partial<CreateProductCategoryPayload>
  ) {
    const existingCategory = (await this.getOne({
      organization_id: orgId,
      id: categoryId,
    })) as ProductCategory;

    const categoryWithNewName = await this.getManyAndCount({
      organization_id: orgId,
      name: updates.name,
    });
    if (categoryWithNewName.count > 0) {
      throw new ConflictError(Errors.CATEGORY_WITH_NEW_EXISTS);
    }

    existingCategory.set(updates);
    const updatedCategory = await existingCategory.save();
    return updatedCategory.get({ plain: true });
  }

  async deleteACategory(orgId: string, categoryId: string) {
    const category = (await this.getOne({
      organization_id: orgId,
      id: categoryId,
    })) as ProductCategory;

    const categoryName = category.name;

    await category.destroy();

    return categoryName;
  }

  async getCategories(where: WhereOptions<ProductCategoryAttributes>, options: FindOptions = {}) {
    return (await this.getManyAndCount(where, options, true)) as {
      rows: ProductCategoryAttributes[];
      count: number;
    };
  }

  async createBulkCategories(orgId: string, payload: CreateProductCategoryPayload[]) {
    const categoryNames = [...new Set(payload.map((p) => p.name))];

    // Fetch existing categories for org
    const existingCategoryNames = (
      await this.getManyAndCount(
        { organization_id: orgId, name: { [Op.in]: categoryNames } },
        {},
        true
      )
    ).rows.map((c) => c.name);

    const categoriesToBeSkipped: SkippedCategory[] = [];
    const categoriesToBeCreated: CreateProductCategoryPayload[] = [];

    for (const category of payload) {
      const { name } = category;

      if (existingCategoryNames.includes(name)) {
        categoriesToBeSkipped.push({
          ...category,
          reason: 'category with same name already exists',
        });
      } else {
        categoriesToBeCreated.push({
          ...category,
          organization_id: orgId,
        });
      }
    }

    if (categoriesToBeSkipped.length > 0) {
      return { skipped: categoriesToBeSkipped };
    }

    const createdCategories = await sequelize.transaction(async (transaction) => {
      return (await this.bulkCreate(
        categoriesToBeCreated,
        { transaction },
        true
      )) as ProductCategoryAttributes[];
    });

    return { created: createdCategories };
  }
}
