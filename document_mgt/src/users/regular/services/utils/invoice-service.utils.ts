import Invoice from '../../../../models/invoice.models';
import Document from '../../../../models/documents.model';
import { Op } from 'sequelize';
// import User from '../../../../models/user.model';
// import Business from '../../../../models/business.model';
import Receipt from '../../../../models/receipt.models';
import { InvoiceData } from '../../../../types/document.types';
import CreditNote from '../../../../models/credit-note.models';
import { DOCUMENT_TYPE } from '../../../../constants/values.constants';
import ReceiptUtils from './receipt-service.utils';
import { getUsersDetails } from '../../../../api/user';
import { ErrorWrapper } from '../../../../helpers/class.helpers';

export default class InvoiceUtils extends ErrorWrapper {
  public static async getInvoiceByRegNumber(invoiceNumber: string): Promise<Invoice | null> {
    const invoice: Invoice | null = await Invoice.findOne({
      where: { invoiceNumber },
      include: [
        {
          model: Document,
          attributes: ['id', 'status', 'documentNumber', 'type', 'createdAt'],
        },
      ],
    });
    return invoice;
  }

  public static async getInvoiceByRegAndorgId(
    invoiceNumber: string,
    orgId: string
  ): Promise<Invoice | null> {
    const invoice: Invoice | null = await Invoice.findOne({
      where: { invoiceNumber, orgId },
    });
    return invoice;
  }

  public static async getTotalInvoiceStat(timeFrame?: [Date, Date]): Promise<number> {
    let createdAtCondition = {};
    if (timeFrame) {
      const [startDate, endDate] = timeFrame;
      createdAtCondition = { [Op.between]: [startDate, endDate] };
    }
    const data: Document[] = await Document.findAll({
      attributes: [[Document.sequelize.fn('count', Document.sequelize.col('*')), 'total_invoices']],
      where: {
        type: 'invoice',
        createdAt: createdAtCondition,
      },
    });
    const totalInvoice: number = Number(data[0]?.get('total_invoices') || 0);
    return totalInvoice;
  }

  public static async getTopGrossingInvoice(timeFrame: [Date, Date]) {
    let createdAtCondition = {};
    if (timeFrame) {
      const [startDate, endDate] = timeFrame;
      createdAtCondition = { [Op.between]: [startDate, endDate] };
    }
    const aggregatedDoc: Document[] = await Document.findAll({
      attributes: [
        [Document.sequelize.fn('count', Document.sequelize.col('*')), 'total_invoice'],
        [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'total_amount'],
        'orgId',
      ],
      where: {
        type: DOCUMENT_TYPE.INVOICE,
        createdAt: createdAtCondition,
        status: {
          [Op.ne]: 'draft',
        },
      },
      group: ['orgId'],
      order: [
        [Document.sequelize.literal('total_invoice'), 'DESC'],
        [Document.sequelize.literal('total_amount'), 'DESC'],
      ],
      limit: 6,
    });
    const orgIds: string[] = aggregatedDoc.map((doc) => doc.orgId);
    const usersData = await getUsersDetails(orgIds);
    if (!usersData) return;
    const data = aggregatedDoc.map((doc) => {
      const user = usersData.find((u) => u.orgId === doc.orgId);
      return {
        total_invoice: doc.dataValues['total_invoice'],
        total_amount: doc.dataValues['total_amount'],
        user,
      };
    });
    return data;
  }

  public static async getInvoiceData(data: any): Promise<InvoiceData[]> {
    const result = [];
    for (const obj of data) {
      const invoice = await Invoice.findOne({
        where: { invoiceNumber: obj.documentNumber, orgId: obj.orgId },
      });
      const dataObj = {
        docId: obj.id,
        invoiceId: invoice.id,
        type: obj.type,
        documentNumber: obj.documentNumber,
        customerName: obj.customerName,
        status: obj.status,
        refunded: obj.refunded,
        createdAt: obj.createdAt,
        updatedAt: obj.updatedAt,
      };
      result.push(dataObj);
    }
    return result;
  }

  public static async getInvoiceByID(id: number): Promise<Invoice | null> {
    const invoice: Invoice | null = await Invoice.findOne({
      where: { id },
      include: [
        {
          model: Receipt,
          attributes: ['receiptNumber', 'createdAt'],
        },
        {
          model: CreditNote,
          attributes: ['creditNoteNumber', 'createdAt'],
        },
      ],
      attributes: ['id', 'docId', 'invoiceNumber', 'createdAt'],
    });
    return invoice;
  }

  public static async getInvoiceByIDAndOrgId(id: number, orgId: string): Promise<Invoice> {
    const invoice = await Invoice.findOne({
      where: { id, orgId },
      attributes: ['id', 'docId', 'invoiceNumber', 'createdAt'],
      include: [
        {
          model: Receipt,
          attributes: ['receiptNumber', 'createdAt'],
        },
        {
          model: CreditNote,
          attributes: ['creditNoteNumber', 'createdAt'],
        },
        {
          model: Document,
          attributes: ['id'],
        },
      ],
    });

    return invoice;
  }

  public static async getInvoiceDocuments(id: string) {
    const document = await Document.findOne({
      where: { id },
      attributes: ['id', 'status', 'documentNumber', 'orgId', 'type', 'createdAt'],
      include: [
        {
          model: Receipt,
          attributes: ['receiptNumber', 'createdAt'],
          include: [
            {
              model: Document,
              attributes: ['status', 'documentNumber', 'type', 'createdAt'],
            },
            {
              model: Invoice,
              attributes: ['invoiceNumber', 'createdAt'],
              include: [
                {
                  model: Document,
                  attributes: ['status', 'documentNumber', 'type', 'createdAt'],
                },
              ],
            },
          ],
        },
        {
          model: CreditNote,
          attributes: ['creditNoteNumber', 'createdAt'],
          include: [
            {
              model: Document,
              attributes: ['status', 'documentNumber', 'type', 'createdAt'],
            },
            {
              model: Invoice,
              attributes: ['invoiceNumber', 'createdAt'],
              include: [
                {
                  model: Document,
                  attributes: ['status', 'documentNumber', 'type', 'createdAt'],
                },
              ],
            },
          ],
        },
        {
          model: Invoice,
          attributes: ['invoiceNumber', 'createdAt'],
          include: [
            {
              model: Document,
              attributes: ['status', 'documentNumber', 'type', 'createdAt'],
            },
            {
              model: Receipt,
              attributes: ['receiptNumber', 'createdAt'],
              include: [
                {
                  model: Document,
                  attributes: ['status', 'documentNumber', 'type', 'createdAt'],
                },
              ],
            },
            {
              model: CreditNote,
              attributes: ['creditNoteNumber', 'createdAt'],
              include: [
                {
                  model: Document,
                  attributes: ['status', 'documentNumber', 'type', 'createdAt'],
                },
              ],
            },
          ],
        },
      ],
    });
    if (!document) return [];
    let invoiceDocuments = [];
    if (document.type === DOCUMENT_TYPE.INVOICE) {
      invoiceDocuments = [
        ...(document['Invoice']['Receipts']?.map((receipt) => ({
          type: 'receipt',
          documentNumber: receipt.receiptNumber,
          status: receipt['Document']['status'],
          createdAt: receipt.createdAt,
        })) || []),
        ...(document['Invoice']['creditNotes'].map((creditnote) => ({
          type: 'creditnote',
          documentNumber: creditnote.creditNoteNumber,
          status: creditnote['Document']['status'],
          createdAt: creditnote.createdAt,
        })) || []),
      ];
    }
    if (document.type === DOCUMENT_TYPE.RECEIPT) {
      const receipt = await ReceiptUtils.getInvoiceDocument(
        document.documentNumber,
        document.orgId
      );
      invoiceDocuments = [
        {
          type: 'invoice',
          documentNumber: receipt['Invoice'].invoiceNumber,
          status: receipt['Invoice']['Document']?.status,
          createdAt: receipt['Invoice'].createdAt,
        },
      ];
    }
    if (document.type === DOCUMENT_TYPE.CREDIT_NOTE) {
      invoiceDocuments = [
        {
          type: 'invoice',
          documentNumber: document['creditNote']['Invoice'].invoiceNumber,
          status: document['creditNote']['Invoice']['Document']?.status,
          createdAt: document['creditNote']['Invoice'].createdAt,
        },
      ];
    }

    return invoiceDocuments;
  }

  public static async getInvoiceDocument(invoiceNumber: string) {
    const invoice: Invoice | null = await Invoice.findOne({
      where: { invoiceNumber },
      attributes: ['id', 'invoiceNumber', 'createdAt'],
      include: [
        {
          model: Document,
          attributes: ['id', 'status', 'documentNumber', 'type', 'createdAt'],
        },
      ],
    });

    return invoice;
  }
}
