import { truncateToTwoDecimals } from '../../../../utilities/global.utilities';
import { GrossingInvoiceType } from '../../../../types/document.types';
import { Request } from 'express';
import createTimezoneConverter from '@candourits/be-timezone-converter';
import { ErrorWrapper } from '../../../../helpers/class.helpers';
import { IAuthenticatedUser } from '../../../../interfaces/user.interfaces';

interface DocumentWithUserData {
  user: IAuthenticatedUser | undefined;
  total_invoice: number;
  total_amount: number;
}

export default class StatsUtils extends ErrorWrapper {
  private static convertDateToTimezone(d: Date, req: Request) {
    return createTimezoneConverter().convertDate(d, req.userTimezone);
  }

  public static getTopOrganizationData(data: any): GrossingInvoiceType[] {
    const topOrganizationData: GrossingInvoiceType[] = [];
    for (const res of data) {
      const documentData = res as unknown as DocumentWithUserData;
      const totalInvoice: number = Number(documentData.total_invoice);
      const totalInvoiceValue: number = Number(documentData.total_amount);
      const organizationData: GrossingInvoiceType = {
        organization: documentData.user ? documentData.user.organization.name : null,
        totalInvoice,
        totalInvoiceValue,
      };
      topOrganizationData.push(organizationData);
    }
    return topOrganizationData;
  }

  public static getTimeFrame(filter: any, req: Request) {
    const now = new Date();

    const startOfDay = (date: Date) => {
      return new Date(
        Date.UTC(
          date.getFullYear(),
          date.getMonth(),
          date.getDate(),
          now.getUTCHours(),
          now.getUTCMinutes(),
          now.getUTCSeconds(),
          now.getUTCMilliseconds()
        )
      );
    };

    const endOfDay = (date: Date) => {
      return new Date(
        Date.UTC(
          date.getFullYear(),
          date.getMonth(),
          date.getDate(),
          now.getUTCHours(),
          now.getUTCMinutes(),
          now.getUTCSeconds(),
          now.getUTCMilliseconds()
        )
      );
    };

    const last48hours = startOfDay(new Date(now.getTime() - 48 * 60 * 60 * 1000));
    const prevDay = endOfDay(new Date(now.getTime() - 24 * 60 * 60 * 1000));

    const startOfOldWeek = startOfDay(
      new Date(now.getFullYear(), now.getMonth(), now.getDate() - 14)
    );
    const prevWeek = endOfDay(
      new Date(
        startOfOldWeek.getFullYear(),
        startOfOldWeek.getMonth(),
        startOfOldWeek.getDate() + 7
      )
    );

    const startOfOldMonth = startOfDay(
      new Date(now.getFullYear(), now.getMonth() - 2, now.getDate())
    );
    const prevMonth = endOfDay(new Date(now.getFullYear(), now.getMonth() - 1, now.getDate()));

    const startOfOldYear = startOfDay(
      new Date(now.getFullYear() - 2, now.getMonth(), now.getDate())
    );
    const prevYear = endOfDay(new Date(now.getFullYear() - 1, now.getMonth(), now.getDate()));

    const last24hours = startOfDay(new Date(now.getTime() - 24 * 60 * 60 * 1000));
    const currentDay = endOfDay(new Date(now.getTime()));

    const startOfPreviousWeek = startOfDay(
      new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7)
    );
    const currentWeek = endOfDay(
      new Date(
        startOfPreviousWeek.getFullYear(),
        startOfPreviousWeek.getMonth(),
        startOfPreviousWeek.getDate() + 7
      )
    );

    const startOfPreviousMonth = startOfDay(
      new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
    );
    const currentMonth = endOfDay(new Date(now.getFullYear(), now.getMonth(), now.getDate()));

    const startOfPreviousYear = startOfDay(
      new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
    );
    const currentYear = endOfDay(new Date(now.getFullYear(), now.getMonth(), now.getDate()));

    type DateRangeMapType = {
      daily: [Date | string, Date | string];
      weekly: [Date | string, Date | string];
      monthly: [Date | string, Date | string];
      annual: [Date | string, Date | string];
    };

    const previousTimeFrames: DateRangeMapType = {
      daily: [
        this.convertDateToTimezone(last48hours, req),
        this.convertDateToTimezone(prevDay, req),
      ],
      weekly: [
        this.convertDateToTimezone(startOfOldWeek, req),
        this.convertDateToTimezone(prevWeek, req),
      ],
      monthly: [
        this.convertDateToTimezone(startOfOldMonth, req),
        this.convertDateToTimezone(prevMonth, req),
      ],
      annual: [
        this.convertDateToTimezone(startOfOldYear, req),
        this.convertDateToTimezone(prevYear, req),
      ],
    };

    const currentTimeFrames: DateRangeMapType = {
      daily: [
        this.convertDateToTimezone(last24hours, req),
        this.convertDateToTimezone(currentDay, req),
      ],
      weekly: [
        this.convertDateToTimezone(startOfPreviousWeek, req),
        this.convertDateToTimezone(currentWeek, req),
      ],
      monthly: [
        this.convertDateToTimezone(startOfPreviousMonth, req),
        this.convertDateToTimezone(currentMonth, req),
      ],
      annual: [
        this.convertDateToTimezone(startOfPreviousYear, req),
        this.convertDateToTimezone(currentYear, req),
      ],
    };

    const currentTimeFrame = currentTimeFrames[filter];
    const prevTimeFrame = previousTimeFrames[filter];
    return { prevTimeFrame, currentTimeFrame };
  }

  public static getPercentageChange(
    previousPeriodSales: number,
    currentPeriodSales: number
  ): number {
    if (previousPeriodSales === 0 && currentPeriodSales > 0) return 100;
    else if (previousPeriodSales === 0 && currentPeriodSales === 0) return 0;
    else {
      const value = ((currentPeriodSales - previousPeriodSales) / previousPeriodSales) * 100;
      return truncateToTwoDecimals(value);
    }
  }
}
