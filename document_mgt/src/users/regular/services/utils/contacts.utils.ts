import { Op } from 'sequelize';
import Contact from '../../../../models/customer-contacts.model';
import { ErrorWrapper } from '../../../../helpers/class.helpers';

export default class CustomerContactUtils extends ErrorWrapper {
  public static async updateContact(data: Record<string, any>): Promise<void> {
    if (!data.id) {
      delete data.id;
      await Contact.create({ ...data });
    } else
      await Contact.update(data, {
        where: { id: data.id, customerId: data.customerId },
      });
  }

  public static async bulkDeleteContacts(customerId: string, ids: string[]): Promise<boolean> {
    await Contact.destroy({
      where: {
        customerId,
        id: {
          [Op.notIn]: ids,
        },
      },
    });
    return true;
  }
}
