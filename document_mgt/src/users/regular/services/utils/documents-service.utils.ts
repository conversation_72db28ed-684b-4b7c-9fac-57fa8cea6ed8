import Document, { DocumentAttributes } from '../../../../models/documents.model';
import invoiceUtils from './invoice-service.utils';
import {
  calcItemAmountAndGetAmounts,
  calcServiceAmountAndGetAmounts,
  calcSubTotalAmount,
  calcTotalAmount,
  incrementDocumentNumber,
  truncateToTwoDecimals,
} from '../../../../utilities/global.utilities';
import invoiceServices from '../invoice.service';
import { Errors } from '../../../../constants/errors.constants';
import {
  DOCUMENT_ENTITY_TYPE,
  DOCUMENT_STATUS,
  DOCUMENT_TYPE,
  ZERO_DOCUMENT_NUMBER,
} from '../../../../constants/values.constants';
import ServiceServices from '../service.service';
import { isProductEntity, isSuperAdmin } from '../../../../utilities/guards';
import {
  DocumentStatusTypes,
  DocumentTypes,
  InvoiceCreditNoteResultType,
} from '../../../../types/document.types';
import ItemServices from '../items.service';
import Invoice from '../../../../models/invoice.models';
// import Receipt from '../../../../models/receipt.models';
// import CreditNote from '../../../../models/credit-note.models';
import CustomerServices from '../customer.service';
import { Model, ModelStatic, Op, Transaction } from 'sequelize';
import CreditNoteUtils from './credit-note-service.utils.';
import Items from '../../../../models/item.model';
import Services from '../../../../models/service.model';
import { ErrorWrapper } from '../../../../helpers/class.helpers';
import { BadRequestError, NotFoundError } from '../../../../middlewares/error_handlers/app-error';

export default class DocumentUtils extends ErrorWrapper {
  public static async getDocument(filter: any): Promise<Document> {
    const document = await Document.findOne({
      ...filter,
      include: { model: Invoice, attributes: ['id'] },
    });

    return document;
  }

  public static async updateDocumentById(id: string, data: Record<string, any>): Promise<boolean> {
    try {
      await Document.update(data, {
        where: { id },
      });

      return true;
    } catch (error) {
      console.error(error.message);
      return false;
    }
  }

  public static async extractIds(data: any): Promise<string[]> {
    let ids: string[];
    if (data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT) {
      ids = data.items.map((item: any) => item.id);
    }
    if (data.entityType === DOCUMENT_ENTITY_TYPE.SERVICE) {
      ids = data.services.map((service: any) => service.id);
    }
    return ids;
  }

  public static async getDocumentByID(
    id: string,
    orgId: string,
    itemService: ItemServices,
    serviceService: ServiceServices,
    customerService: CustomerServices,
    status?: string
  ): Promise<any> {
    const filter = { where: { id } };

    if (status) {
      filter.where['status'] = status;
    }

    if (orgId) {
      filter.where['orgId'] = orgId;
    }

    try {
      const document: Document = await this.getDocument(filter);

      if (!document) throw new NotFoundError(Errors.DOCUMENT_NOT_FOUND);

      if (document.type === DOCUMENT_TYPE.CREDIT_NOTE) {
        const creditNoteWithAssociatedInvoice =
          await CreditNoteUtils.getCreditNoteWithAssociatedDocumentAndInvoiceByDocId(
            orgId,
            document.docId
          );

        const invoiceDocument = creditNoteWithAssociatedInvoice.get('Invoice')['Document'];

        const invoiceCreditNoteIssuableAmount = truncateToTwoDecimals(
          invoiceDocument.totalAmount - invoiceDocument.creditNoteAmount
        );

        document.dataValues['creditNoteIssuableAmount'] = invoiceCreditNoteIssuableAmount;
      } else {
        document.dataValues['creditNoteIssuableAmount'] = document['creditNoteIssuableAmount'];
      }

      if (document.type === DOCUMENT_TYPE.INVOICE) {
        document.dataValues['invoiceId'] = document.get('Invoice')['id'];
      }

      delete document.dataValues['Invoice'];
      const data = await this.getDocumentData(
        orgId,
        document,
        itemService,
        serviceService,
        customerService
      );

      return data;
    } catch (error) {
      console.error(error.message);
      throw error;
    }
  }

  public static async filterDocument(
    type: string,
    whereCondition: Record<string, any>,
    paginate: Record<string, number>
  ) {
    const commonAttributes = [
      'id',
      'docId',
      'type',
      'entityType',
      'documentNumber',
      'notes',
      'customerId',
      'updatedAt',
      'createdAt',
      'dateIssued',
      'dueDate',
      'totalAmount',
      'status',
    ];

    let specificAttributes: string[] = [];

    switch (type) {
      case DOCUMENT_TYPE.INVOICE:
        specificAttributes = ['totalVat', 'totalDiscount'];
        break;
      case DOCUMENT_TYPE.RECEIPT:
        specificAttributes = ['totalVat'];
        break;
      case DOCUMENT_TYPE.CREDIT_NOTE:
        specificAttributes = ['totalDiscount'];
        break;
    }

    const documents = await Document.findAndCountAll({
      where: whereCondition,
      ...paginate,
      order: [['createdAt', 'DESC']],
      attributes: [...commonAttributes, ...specificAttributes],
    });

    return documents;
  }

  public static async attachLinkedData(
    orgId: string,
    documents: Document[],
    itemService: ItemServices,
    serviceService: ServiceServices,
    customerService: CustomerServices
  ): Promise<any[]> {
    const responseData = [];

    for (const document of documents) {
      if (!orgId) orgId = document.orgId;

      const res = await this.getDocumentByID(
        document.id,
        orgId,
        itemService,
        serviceService,
        customerService
      );

      if (!res) return;

      responseData.push(res);
    }

    return responseData;
  }

  public static async getDocumentData(
    orgId: string,
    documentData: Document,
    itemService: ItemServices,
    serviceService: ServiceServices,
    customerService: CustomerServices
  ): Promise<any> {
    let entities;
    if (DOCUMENT_ENTITY_TYPE.PRODUCT === documentData.dataValues.entityType) {
      entities = await itemService.getItemsByDocId(documentData.dataValues.docId, orgId);
    }

    if (DOCUMENT_ENTITY_TYPE.SERVICE === documentData.dataValues.entityType) {
      entities = await serviceService.getServicesByDocId(documentData.dataValues.docId, orgId);
    }

    const invoiceDocuments = await invoiceUtils.getInvoiceDocuments(documentData.dataValues.id);

    const customer = await customerService.getCustomer(documentData.dataValues.customerId, orgId);
    const customerData = {
      customerName: customer.name,
      customerEmail: customer.email,
      customerAddress: customer.address,
      customerPhoneNumber: customer.phoneNumber,
    };

    const dataValues = { ...documentData.dataValues, ...customerData };
    dataValues['entities'] = entities;
    dataValues['linkedDocuments'] = invoiceDocuments;

    return dataValues;
  }

  public static async totalCount(parameters: any): Promise<number> {
    const totalCount: number = await Document.count({
      where: parameters,
    });
    return totalCount;
  }

  public static async invoiceDocumentType(
    type: string,
    lastDocument: string,
    documentData: Record<string, any>,
    invoiceService: invoiceServices
  ): Promise<Record<string, any>> {
    if (type === DOCUMENT_TYPE.INVOICE) {
      const getNextDocId = await incrementDocumentNumber('INV', lastDocument);

      documentData.documentNumber = getNextDocId;
      const invoicePayload = {
        invoiceNumber: documentData.documentNumber,
        orgId: documentData.orgId,
        dateIssued: documentData.dateIssued,
        dueDate: documentData.dueDate,
        createdAt: documentData.createdAt,
        updatedAt: documentData.updatedAt,
      };
      const responseData = await invoiceService.createInvoice(invoicePayload);

      if (responseData) documentData.invoiceId = responseData.id;
    }
    return documentData;
  }

  public static async updateDocumentItems(data: Record<string, any>) {
    const { itemData, amounts, totalVat, totalDiscount } = calcItemAmountAndGetAmounts(data.items);
    data.subTotalAmount = calcSubTotalAmount(amounts);
    data.totalAmount = calcTotalAmount(data.subTotalAmount, totalVat, totalDiscount);
    if (data.totalAmount === 0) throw new BadRequestError(Errors.AMOUNT_IS_ZERO);
    data.items = itemData;
    data.totalVat = totalVat;
    data.totalDiscount = totalDiscount;
    return data;
  }

  public static async updateDocumentServices(data: Record<string, any>) {
    const { serviceData, amounts, totalVat, totalDiscount } = calcServiceAmountAndGetAmounts(
      data.services
    );
    data.subTotalAmount = calcSubTotalAmount(amounts);
    data.totalAmount = calcTotalAmount(data.subTotalAmount, totalVat, totalDiscount);
    if (data.totalAmount === 0) throw new BadRequestError(Errors.AMOUNT_IS_ZERO);
    data.services = serviceData;
    data.totalVat = totalVat;
    data.totalDiscount = totalDiscount;
    return data;
  }

  public static removeUnwantedDocumentData(role: string, data, type) {
    if (!isSuperAdmin(role)) {
      delete data.docId;
      delete data.orgId;
      switch (type) {
        case DOCUMENT_TYPE.INVOICE:
          delete data.receiptId;
          delete data.creditNoteId;
          break;
        case DOCUMENT_TYPE.RECEIPT:
          delete data.dueDate;
          delete data.creditNoteId;
          break;
        case DOCUMENT_TYPE.CREDIT_NOTE:
          delete data.dueDate;
          delete data.receiptId;
      }
    }
  }

  public static async updateInvoiceEntities(
    data: Record<string, any>,
    ItemService: ItemServices,
    ServiceService: ServiceServices,
    transaction?: Transaction
  ) {
    let calculatedEntitiesAmounts;

    if (data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT) {
      if (data?.items && data.items.length > 0) {
        calculatedEntitiesAmounts = calcItemAmountAndGetAmounts(data.items);
        for (const entity of data.items) {
          entity.orgId = data.orgId;
          await ItemService.updateByDocId(data.docId, entity.orgId, entity, transaction);
        }
      }
    }

    if (data.entityType === DOCUMENT_ENTITY_TYPE.SERVICE) {
      if (data?.services && data.services.length > 0) {
        calculatedEntitiesAmounts = calcServiceAmountAndGetAmounts(data.services);
        for (const entity of data.services) {
          entity.orgId = data.orgId;
          await ServiceService.updateByDocId(data.docId, entity.orgId, entity, transaction);
        }
      }
    }
    return calculatedEntitiesAmounts;
  }

  public static async getModelIds(
    queryModel: ModelStatic<Model>,
    ids: string[],
    orgId: string,
    docId: number
  ) {
    const foundItems = await queryModel.findAll({
      where: {
        orgId,
        docId,
        id: {
          [Op.in]: ids,
        },
      },
    });
    const foundIds = foundItems.map((item) => item.get('id'));
    const missingIds = ids.filter((id) => !foundIds.includes(id));
    if (missingIds.length > 0)
      throw new BadRequestError(`missing IDs in the database: ${missingIds.join(', ')}`);
    return foundItems;
  }

  public static async updateCreditNoteEntities(
    data: Record<string, any>,
    ItemService: ItemServices,
    ServiceService: ServiceServices,
    transaction: Transaction
  ) {
    let calculatedEntitiesAmounts;

    if (data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT) {
      if (data?.items && data.items.length > 0) {
        const items = data.items;
        const ids = items.map((item) => item.id);
        const foundItems = await this.getModelIds(Items, ids, data.orgId, data.docId);
        if (!foundItems) return;
        for (const entity of data.items) {
          const item = foundItems.find((item) => item.get('id') === entity.id);
          entity.orgId = data.orgId;
          entity.vat = item.get('vat');
          entity.discount = item.get('discount');
          await ItemService.updateByDocId(data.docId, entity.orgId, entity, transaction);
        }
        calculatedEntitiesAmounts = calcItemAmountAndGetAmounts(data.items);
      }
    }

    if (data.entityType === DOCUMENT_ENTITY_TYPE.SERVICE) {
      if (data?.services && data.services.length > 0) {
        const services = data.services;
        const ids = services.map((service) => service.id);
        const foundServices = await this.getModelIds(Services, ids, data.orgId, data.docId);
        if (!foundServices) return;
        for (const entity of data.services) {
          const service = foundServices.find((service) => service.get('id') === entity.id);
          entity.orgId = data.orgId;
          entity.vat = service.get('vat');
          entity.discount = service.get('discount');
          await ServiceService.updateByDocId(data.docId, entity.orgId, entity, transaction);
        }
        calculatedEntitiesAmounts = calcServiceAmountAndGetAmounts(data.services);
      }
    }
    return calculatedEntitiesAmounts;
  }

  public static async processEntityAmountsAndUpdateData(
    data: Record<string, any>,
    ItemService: ItemServices,
    ServiceService: ServiceServices,
    transaction: Transaction
  ) {
    let calculatedEntitiesAmounts;

    if (data.type === DOCUMENT_TYPE.CREDIT_NOTE)
      calculatedEntitiesAmounts = await this.updateCreditNoteEntities(
        data,
        ItemService,
        ServiceService,
        transaction
      );

    if (data.type === DOCUMENT_TYPE.INVOICE)
      calculatedEntitiesAmounts = await this.updateInvoiceEntities(
        data,
        ItemService,
        ServiceService,
        transaction
      );

    const { amounts, totalVat, totalDiscount } = calculatedEntitiesAmounts;
    data.subTotalAmount = calcSubTotalAmount(amounts);
    data.totalAmount = calcTotalAmount(data.subTotalAmount, totalVat, totalDiscount);

    if (data.totalAmount === 0) throw new BadRequestError(Errors.AMOUNT_IS_ZERO);

    data.totalVat = truncateToTwoDecimals(totalVat);
    data.totalDiscount = truncateToTwoDecimals(totalDiscount);
    data.remainingPayableAmount = truncateToTwoDecimals(data.totalAmount);

    delete data.entities;
  }

  public static calculateInvoiceCreditNoteDifference(
    invoiceData: DocumentAttributes,
    creditnoteData
  ): InvoiceCreditNoteResultType {
    const creditNoteAmount = creditnoteData.totalAmount;
    const invoiceAmount = invoiceData.totalAmount;
    const amountPaid = invoiceData.amountPaid;
    const amountDifference = invoiceAmount - creditNoteAmount;
    let result: InvoiceCreditNoteResultType;

    if (creditnoteData.draft) {
      return (result = {
        refundable: invoiceData.refundableAmount,
        remainingPayableAmount: invoiceData.remainingPayableAmount,
        refundStatus: invoiceData.refunded,
        invoiceStatus: invoiceData.status,
      });
    }

    if (!amountPaid && creditNoteAmount === invoiceAmount) {
      result = {
        refundable: creditNoteAmount,
        remainingPayableAmount: 0,
        refundStatus: true,
        invoiceStatus: DOCUMENT_STATUS.COMPLETED,
      };
    }

    if (!amountPaid && creditNoteAmount < invoiceAmount) {
      result = {
        refundable: creditNoteAmount,
        remainingPayableAmount: invoiceAmount - creditNoteAmount,
        refundStatus: true,
        invoiceStatus: DOCUMENT_STATUS.AWAITING_PAYMENT,
      };
    }

    if (amountPaid > 0 && creditNoteAmount === invoiceAmount) {
      result = {
        refundable: amountPaid + (invoiceAmount - creditNoteAmount),
        remainingPayableAmount: 0,
        refundStatus: false,
        invoiceStatus: DOCUMENT_STATUS.COMPLETED,
      };
    }

    if (amountPaid > 0 && amountDifference > amountPaid) {
      result = {
        refundable: creditNoteAmount,
        remainingPayableAmount: amountDifference - amountPaid,
        refundStatus: true,
        invoiceStatus: DOCUMENT_STATUS.PARTIAL_PAYMENT,
      };
    }

    if (amountPaid > 0 && amountDifference < amountPaid) {
      result = {
        refundable: amountPaid - amountDifference,
        remainingPayableAmount: 0,
        refundStatus: false,
        invoiceStatus: DOCUMENT_STATUS.COMPLETED,
      };
    }

    if (amountPaid > 0 && amountDifference === amountPaid) {
      result = {
        refundable: creditNoteAmount,
        remainingPayableAmount: 0,
        refundStatus: true,
        invoiceStatus: DOCUMENT_STATUS.COMPLETED,
      };
    }
    result.refundable = truncateToTwoDecimals(result.refundable);
    result.remainingPayableAmount = truncateToTwoDecimals(result.remainingPayableAmount);
    return result;
  }

  public static getLastDocumentNumber(document: DocumentAttributes, docType: string) {
    return document ? document.documentNumber : ZERO_DOCUMENT_NUMBER[docType];

    // if (document && docType) {
    //   if (docType === DOCUMENT_TYPE.INVOICE) regNumber = document.invoiceNumber;
    //   if (docType === DOCUMENT_TYPE.RECEIPT) regNumber = document.receiptNumber;
    //   if (docType === DOCUMENT_TYPE.CREDIT_NOTE) regNumber = document.creditNoteNumber;
    // } else {
    //   if (docType === DOCUMENT_TYPE.INVOICE) regNumber = 'INV-000';
    //   if (docType === DOCUMENT_TYPE.RECEIPT) regNumber = 'REC-000';
    //   if (docType === DOCUMENT_TYPE.CREDIT_NOTE) regNumber = 'CRN-000';
    // }

    // return regNumber;
  }

  public static async getLastDocumentByDocType(docType: DocumentTypes, orgId: string) {
    const document = await Document.findAll({
      where: { orgId, type: docType, status: { [Op.not]: DOCUMENT_STATUS.DRAFT } },
      order: [['documentNumber', 'DESC']],
      limit: 1,
    });

    return document.length > 0 ? document[0].toJSON() : null;

    // let document: Invoice[] | Receipt[] | CreditNote[];
    // if (docType === DOCUMENT_TYPE.INVOICE) {
    //   document = await Invoice.findAll({
    //     where: { orgId },
    //     order: [['createdAt', 'DESC']],
    //     limit: 1,
    //   });
    // }
    // if (docType === DOCUMENT_TYPE.RECEIPT) {
    //   document = await Receipt.findAll({
    //     where: { orgId },
    //     order: [['createdAt', 'DESC']],
    //     limit: 1,
    //   });
    // }
    // if (docType === DOCUMENT_TYPE.CREDIT_NOTE) {
    //   document = await CreditNote.findAll({
    //     where: { orgId },
    //     order: [['createdAt', 'DESC']],
    //     limit: 1,
    //   });
    // }
  }

  static async getLastDocumentByStatus(orgId: string, status: DocumentStatusTypes) {
    const document = await Document.findAll({
      where: { orgId, status },
      order: [['documentNumber', 'DESC']],
      limit: 1,
    });

    return document.length > 0 ? document[0].toJSON() : null;
  }

  public static async getNextDocumentNumber(
    orgId: string,
    docType: DocumentTypes,
    isDraft: boolean = false
  ): Promise<string> {
    // handle draft document
    if (isDraft) {
      return '';
    }

    // handle non draft document
    const document = await DocumentUtils.getLastDocumentByDocType(docType, orgId);
    const lastDocNumber = DocumentUtils.getLastDocumentNumber(document, docType);
    const documentNumberPrefix = lastDocNumber.split('-')[0];

    const nextDocNumber = await incrementDocumentNumber(documentNumberPrefix, lastDocNumber);

    return nextDocNumber;
  }

  public static async getDocuments(
    condition: Record<string, any>,
    paginate: { offset?: number; limit?: number; page?: number },
    ItemService: ItemServices,
    ServiceService: ServiceServices,
    CustomerService: CustomerServices
  ) {
    let orgId: string;
    if (condition.orgId) orgId = condition.orgId;
    if (condition.customerIds && condition.customerIds.length > 0) {
      condition.customerId = {
        [Op.in]: condition.customerIds,
      };
    }

    delete condition.customerIds;

    const documents = await Document.findAndCountAll({
      where: condition,
      ...paginate,
      attributes: [
        'id',
        'docId',
        'type',
        'entityType',
        'totalVat',
        'totalDiscount',
        'notes',
        'documentNumber',
        'customerId',
        'updatedAt',
        'createdAt',
        'dateIssued',
        'dueDate',
        'totalAmount',
        'status',
        'createdAt',
      ],
      order: [['createdAt', 'DESC']],
    });

    if (documents.count === 0) return { data: [], count: documents.count };

    const data = await DocumentUtils.attachLinkedData(
      orgId,
      documents.rows,
      ItemService,
      ServiceService,
      CustomerService
    );

    return { data, count: documents.count };
  }

  public static async createDocumentEntity(
    data: Record<string, any>,
    newDocumentData: Document,
    itemService: ItemServices,
    serviceService: ServiceServices
  ) {
    if (data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT)
      await itemService.createItems(data['items'], data.orgId, newDocumentData.id);

    if (data.entityType === DOCUMENT_ENTITY_TYPE.SERVICE)
      await serviceService.createServices(data['services'], data.orgId, newDocumentData.id);
  }

  public static async updateDocumentEntity(
    data: Record<string, any>,
    itemService: ItemServices,
    serviceService: ServiceServices
  ) {
    if (data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT)
      await itemService.updateItems(data['items'], data.orgId);
    if (data.entityType === DOCUMENT_ENTITY_TYPE.SERVICE)
      await serviceService.updateServices(data['services'], data.orgId);
  }

  public static async createAndUpdateDocument(
    data: Record<string, any>,
    invoiceDocument: Document,
    itemService: ItemServices,
    serviceService: ServiceServices
  ) {
    data.draft ? (data.status = DOCUMENT_STATUS.DRAFT) : await invoiceDocument.save();

    const responseData = await Document.create(data);
    responseData.docId = responseData.id;
    await responseData.save();

    await this.createDocumentEntity(data, responseData, itemService, serviceService);
    return responseData;
  }

  public static populateCreditNoteData(
    data: Record<string, any>,
    invoiceDocument: any,
    nextCreditNoteNumber: string
  ) {
    data.type = DOCUMENT_TYPE.CREDIT_NOTE;
    data.documentNumber = nextCreditNoteNumber;
    data.customerId = invoiceDocument.customerId;
    data.logo = invoiceDocument.logo;
    data.logoThumbnail = invoiceDocument.logoThumbnail;
    data.businessName = invoiceDocument.businessName;
    data.businessAddress = invoiceDocument.businessAddress;
    data.businessCountry = invoiceDocument.businessCountry;
    data.status = DOCUMENT_STATUS.COMPLETED;
  }

  public static updateInvoiceAndCreditNote(invoiceDocument: any, data: Record<string, any>) {
    invoiceDocument.refundableAmount = truncateToTwoDecimals(
      invoiceDocument.totalAmount - data.totalAmount
    );

    data.refundableAmount = invoiceDocument.refundableAmount;
    const newPayableAmount = truncateToTwoDecimals(
      invoiceDocument.totalAmount - invoiceDocument.refundableAmount
    );

    if (
      newPayableAmount < invoiceDocument.amountPaid ||
      newPayableAmount === invoiceDocument.amountPaid
    ) {
      invoiceDocument.status = DOCUMENT_STATUS.COMPLETED;
      invoiceDocument.remainingPayableAmount = 0;
      data.remainingPayableAmount = 0;
    } else {
      invoiceDocument.status = DOCUMENT_STATUS.PARTIAL_PAYMENT;
      invoiceDocument.remainingPayableAmount = newPayableAmount;
      data.remainingPayableAmount = newPayableAmount;
    }
  }

  public static async createInvoiceAndDocument(
    data: Record<string, any>,
    invoiceService: invoiceServices,
    itemService: ItemServices,
    serviceService: ServiceServices
  ) {
    const documentData = isProductEntity(data.entityType)
      ? await DocumentUtils.updateDocumentItems(data)
      : await DocumentUtils.updateDocumentServices(data);
    if (!documentData) return;

    const isDraft = documentData.status === DOCUMENT_STATUS.DRAFT;

    documentData['documentNumber'] = await this.getNextDocumentNumber(
      documentData.orgId,
      documentData.type,
      isDraft
    );

    const invoicePayload = {
      invoiceNumber: documentData.documentNumber,
      orgId: documentData.orgId,
      dateIssued: documentData.dateIssued,
      dueDate: documentData.dueDate,
      createdAt: documentData.createdAt,
      updatedAt: documentData.updatedAt,
    };

    const newInvoice = await invoiceService.createInvoice(invoicePayload);

    if (!newInvoice) return;

    documentData.remainingPayableAmount = documentData.totalAmount;

    const responseData = await Document.create(documentData);
    newInvoice.docId = responseData.id;
    await newInvoice.save();
    await this.createDocumentEntity(documentData, responseData, itemService, serviceService);

    await DocumentUtils.updateDocumentById(responseData.id, { docId: responseData.id });
    return { ...responseData, invoiceId: newInvoice.id };
  }
}
