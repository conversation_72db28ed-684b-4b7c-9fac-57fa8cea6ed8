import { Op, Transaction } from 'sequelize';
import { Errors } from '../../../constants/errors.constants';
import Services from '../../../models/service.model';
import { ServicesDTOType } from '../../../types/document.types';
import { NotFoundError } from '../../../middlewares/error_handlers/app-error';

export default class ServiceServices {
  public async getServiceById(id: number): Promise<void | Services> {
    const service: Services = await Services.findOne({ where: { id } });
    if (!service) {
      throw new NotFoundError(Errors.SERVICE_NOT_FOUND);
    }
    return service;
  }

  public async getServiceByOrgId(id: string, orgId: string): Promise<void | Services> {
    const service: Services = await Services.findOne({
      where: { id, orgId },
    });
    if (!service) {
      throw new NotFoundError(Errors.SERVICE_NOT_FOUND);
    }
    return service;
  }

  public async getServicesByDocId(docId: string, orgId: string): Promise<Services[]> {
    const filter: Record<string, any> = {};
    filter.where = { docId, orgId };
    const attributes = [
      'id',
      'docId',
      'type',
      'description',
      'hours',
      'hourlyRate',
      'discount',
      'vat',
      'totalFee',
    ];
    const services: Services[] = await Services.findAll({
      ...filter,
      attributes,
    });

    return services.map((service) => service.toJSON());
  }

  public async getServices(paginate: Record<string, number>): Promise<any> {
    const data = await Services.findAll({
      ...paginate,
      order: [['updatedAt', 'DESC']],
    });
    const count = await this.totalCount();
    return { data: data.map((service) => service.toJSON()), count };
  }

  private readonly totalCount = async (orgId?: string) => {
    let whereCondition;
    orgId && (whereCondition = { where: { orgId } });
    const totalCount = await Services.count({ ...whereCondition });
    return totalCount as unknown as number;
  };

  public async getServicesByOrgId(orgId: string, paginate: Record<string, number>) {
    const data = await Services.findAndCountAll({
      where: { orgId },
      ...paginate,
      order: [['updatedAt', 'DESC']],
    });

    return { data: data.rows.map((service) => service.toJSON()), count: data.count };
  }

  public async bulkCreateAndGetIds(data: Services[]): Promise<string[]> {
    const ids: string[] = [];
    const servicePromises: Services[] = await Services.bulkCreate(data);
    servicePromises.forEach((service) => ids.push(service.id));
    return ids;
  }

  public async createServices(data: [], orgId: string, docId: string): Promise<Services[]> {
    data.forEach((service: Record<string, any>) => {
      delete service.id;
      service.docId = docId;
      service.orgId = orgId;
    });
    const services: Services[] = await Services.bulkCreate(data);
    return services;
  }

  public async updateServices(data: [], orgId: string) {
    data.forEach(async (service: Record<string, any>) => {
      await Services.update(service, { where: { orgId, docId: service.docId, id: service.id } });
    });
  }

  public async updateService(id: number, data: Record<string, any>): Promise<void | Services> {
    const service: void | Services = await this.getServiceById(id);
    if (!service) {
      throw new NotFoundError(Errors.SERVICE_NOT_FOUND);
    }
    await Services.update(data, { where: { id } });
  }

  public async updateByDocId(
    docId: string,
    orgId: string,
    data: ServicesDTOType,
    transaction?: Transaction
  ): Promise<void> {
    const { id, ...serviceData } = data;

    if (!id) await Services.create({ docId, orgId, ...serviceData }, { transaction });
    else
      await Services.update(serviceData, {
        where: { id, docId, orgId },
        transaction,
      });

    return;
  }

  public async bulkDeleteServiceByDocId(
    orgId: string,
    docId: string,
    ids: string[],
    transaction: Transaction
  ): Promise<boolean> {
    await Services.destroy({
      where: {
        docId,
        orgId,
        id: {
          [Op.notIn]: ids,
        },
      },
      transaction,
    });
    return true;
  }
}
