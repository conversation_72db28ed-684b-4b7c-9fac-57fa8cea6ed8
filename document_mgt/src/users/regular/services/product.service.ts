import { Op, WhereOptions } from 'sequelize';
import Product, { ProductAttributes } from '../../../models/product.model';
import { isValuePresent } from '../../../utilities/guards';
import { ConflictError } from '../../../middlewares/error_handlers/app-error';
import { Errors } from '../../../constants/errors.constants';
import BaseServices from '../../../base.service';
import { CreateProductPayload } from '../../../interfaces/request-body/inventory-payload.interfaces';
import ProductCategoryServices from './category.service';
import sequelize from '../../../config/database/connection';
import { SearchProduct } from '../../../interfaces/query-params/inventory.query-params.interface';

interface SkippedProducts extends CreateProductPayload {
  reason: string;
}

export default class ProductServices extends BaseServices<Product> {
  constructor(private productCategoryServices: ProductCategoryServices) {
    super(Product);
  }

  // private async getOneWhere(orgId: string, productId: string) {
  //   const product = await Product.findOne({
  //     where: { organization_id: orgId, id: productId },
  //   });

  //   if (!product) {
  //     throw new NotFoundError(Errors.PRODUCT_NOT_FOUND);
  //   }

  //   return product;
  // }

  // private async getManyWhere(
  //   orgId: string,
  //   where: WhereOptions<ProductAttributes>,
  //   offset = 0,
  //   limit = 50
  // ) {
  //   const products = await Product.findAll({
  //     where: { organization_id: orgId, ...where },
  //     attributes: { exclude: ['organization_id'] },
  //     offset,
  //     limit,
  //   });

  //   return products;
  // }

  async getAllProducts(orgId: string, offset: number, limit: number) {
    const result = (await this.getManyAndCount({ organization_id: orgId }, { offset, limit })) as {
      rows: ProductAttributes[];
      count: number;
    };

    return result;
  }

  async getOneProduct(orgId: string, productId: string) {
    const product = (await this.getOne({
      organization_id: orgId,
      id: productId,
    })) as ProductAttributes;

    return product;
  }

  // async updateOneInventory(
  //   orgId: string,
  //   productId: string,
  //   updates: Partial<ProductAttributes>
  // ) {
  //   const product = await this.getOne(orgId, productId);

  //   const existingInventory = await this.getManyWhere(orgId, {
  //     name: updates.name,
  //     category: updates.category,
  //   });

  //   if (existingInventory.length > 0 && existingInventory[0].id !== productId) {
  //     throw new ConflictError(Errors.INVENTORY_ALREADY_EXISTS);
  //   }

  //   if (updates.name === product.name && updates.category === product.category) {
  //     throw new ConflictError(Errors.INVENTORY_NOT_UPDATED);
  //   }

  //   product.set(updates);
  //   const updatedInventory = (await product.save()).toJSON();
  //   return updatedInventory;
  // }

  async deleteOneProduct(orgId: string, productId: string) {
    const product = (await this.getOne(
      { organization_id: orgId, id: productId },
      {},
      false
    )) as Product;

    const productName = product.name;

    product.destroy();
    return productName;
  }

  async createOneProduct(orgId: string, payload: CreateProductPayload) {
    if (isValuePresent(payload.category_id)) {
      await this.productCategoryServices.getOneCategory(orgId, payload.category_id);
    }

    const existingInventory = await this.getOne({ organization_id: orgId, name: payload.name });

    if (isValuePresent(existingInventory)) {
      throw new ConflictError(Errors.PRODUCT_EXISTS);
    }

    const createdInventory = (await this.create({
      ...payload,
      organization_id: orgId,
    })) as ProductAttributes;

    return createdInventory;
  }

  async searchProducts(filter: SearchProduct, orgId: string, offset: number, limit: number) {
    const where: WhereOptions<Partial<ProductAttributes>> = { organization_id: orgId };
    let categoryId = '';

    if (isValuePresent(filter.categoryName)) {
      const category = await this.productCategoryServices.getCategories(
        {
          organization_id: orgId,
          name: filter.categoryName,
        },
        { limit: 1 }
      );

      if (category.count === 0) return { rows: [], count: 0 };

      categoryId = category.rows[0].id;
    }

    if (isValuePresent(filter.productName)) {
      where['name'] = { [Op.iLike]: `%${filter.productName}%` };
    }

    if (isValuePresent(categoryId)) {
      where['category_id'] = categoryId;
    }

    const products = (await this.getManyAndCount(where, { offset, limit })) as {
      rows: ProductAttributes[];
      count: number;
    };

    return products;
  }

  async changeProductStatus(orgId: string, productId: string, newStatus: string) {
    const product = (await this.getOne(
      { organization_id: orgId, id: productId },
      {},
      false
    )) as Product;

    if (product.status === newStatus) {
      throw new ConflictError(Errors.PRODUCT_STATUS_CONFLICT);
    }

    product.status = newStatus;

    return (await product.save({ fields: ['status'] })).toJSON();
  }

  async createBulkProducts(orgId: string, payload: CreateProductPayload[]) {
    // extract unique category ids and product names
    const providedCategoryIds = [...new Set(payload.map((p) => p.category_id).filter(Boolean))];
    const providedProductNames = [...new Set(payload.map((p) => p.name))];

    // get valid category ids from db
    const validCategoryIds = (
      await this.productCategoryServices.getCategories({
        organization_id: orgId,
        id: { [Op.in]: providedCategoryIds },
      })
    ).rows.map((c) => c.id);

    // get existing product names from db
    const existingProductNames = (
      await this.getManyAndCount(
        { organization_id: orgId, name: { [Op.in]: providedProductNames } },
        {}
      )
    ).rows.map((p) => p.name);

    const productsToBeSkipped: SkippedProducts[] = [];
    const productsToBeCreated: CreateProductPayload[] = [];

    // process each document sent for creating
    for (const product of payload) {
      const { name, category_id } = product;

      const reasons: string[] = [];

      if (category_id && !validCategoryIds.includes(category_id)) {
        reasons.push('invalid category');
      }

      if (name && existingProductNames.includes(name)) {
        reasons.push('product name already exists');
      }

      if (reasons.length > 0) {
        productsToBeSkipped.push({ ...product, reason: reasons.join(', ') });
      } else {
        productsToBeCreated.push({ ...product, organization_id: orgId });
      }
    }

    if (productsToBeSkipped.length > 0) {
      return { skipped: productsToBeSkipped };
    }

    // bulk create valid products
    const createdProducts = await sequelize.transaction(async (transaction) => {
      return (await this.bulkCreate(productsToBeCreated, { transaction })) as ProductAttributes[];
    });

    return { created: createdProducts };
  }
}
