import Customer, { CustomerAttributes } from '../../../models/customer.model';
import customerUtils from './utils/customer-service.utils';
import Contact from '../../../models/customer-contacts.model';
import { Op, WhereOptions } from 'sequelize';
import { Errors } from '../../../constants/errors.constants';
import CustomerContactUtils from './utils/contacts.utils';
import { ErrorWrapper } from '../../../helpers/class.helpers';
import { BadRequestError } from '../../../middlewares/error_handlers/app-error';
import { isValuePresent } from '../../../utilities/guards';
import CustomerUtils from './utils/customer-service.utils';
import { CustomerPayload } from '../../../interfaces/request-body/document-payload.interfaces';
import sequelize from '../../../config/database/connection';

export default class CustomerServices extends Error<PERSON>rapper {
  public async createCustomer(
    orgId: string,
    customersPayload: CustomerPayload[],
    role: string
  ): Promise<CustomerAttributes[]> {
    // add orgId to customer payload and normalize customer and contacts emails.
    const customersData = CustomerUtils.processCreateCustomerData(orgId, customersPayload);

    // create customers and contacts in a transaction.
    const result = await sequelize.transaction(async (transaction) => {
      const createdCustomers = await Customer.bulkCreate(customersData, { transaction });

      const contactsList = [];
      for (let i = 0; i < createdCustomers.length; i++) {
        const customer = createdCustomers[i];
        const customerContacts = customersData[i].contacts;

        if (isValuePresent(customerContacts) && Array.isArray(customerContacts)) {
          customerContacts.forEach((contact) => {
            contactsList.push({ ...contact, customerId: customer.id });
          });
        }
      }

      await Contact.bulkCreate(contactsList, { transaction });

      return createdCustomers.map((c) => c.toJSON());
    });

    result.forEach((c) => CustomerUtils.removeUnwantedCustomerData(role, c));

    return result;
  }

  public async createACustomer(data: CustomerAttributes): Promise<CustomerAttributes> {
    const customer: Customer = await Customer.create(data);
    return customer.toJSON();
  }

  public async updateCustomerById(customerId: string, data: Record<string, any>): Promise<any> {
    const orgId = data.orgId;
    const filter = { where: { id: customerId, orgId } };
    const contacts = data.contacts;
    const ids: string[] = await this.extractIds(contacts);

    const bulkDelete = await CustomerContactUtils.bulkDeleteContacts(customerId, ids);

    if (!bulkDelete) throw new BadRequestError(Errors.DELETE_CONTACT_ERROR);

    if (contacts.length > 0) {
      for (const contact of contacts) {
        contact.orgId = data.orgId;
        contact.customerId = customerId;
        await CustomerContactUtils.updateContact(contact);
      }
    }
    delete data.contacts;

    const updatedData = await Customer.update(data, {
      ...filter,
      returning: true,
    });

    const updatedContacts = await Contact.findAll({
      where: { customerId: updatedData[1][0].dataValues.id },
    });

    const updated = { ...updatedData[1][0].dataValues };
    updated['contacts'] = updatedContacts.map((contact) => contact.toJSON());
    return updated;
  }

  public async getCustomers(
    paginate: Record<string, number>,
    orgId: string
  ): Promise<{ data: CustomerAttributes[]; count: number }> {
    const whereCondition = { where: { orgId } };

    const data = await Customer.findAndCountAll({
      ...whereCondition,
      ...paginate,
      include: Contact,
      order: [['createdAt', 'DESC']],
    });

    return { data: data.rows.map((item) => item?.toJSON()), count: data.count };
  }

  private readonly extractIds = async (data: Record<string, any>[]) => {
    const ids: string[] = data.map((item) => {
      if (item.id) return item.id;
    });
    return ids;
  };

  public async getCustomer(customerId: string, orgId: string): Promise<CustomerAttributes> {
    const data = await customerUtils.getCustomerByOrgId(customerId, orgId);
    return data?.toJSON();
  }

  public async searchCustomer(
    customerName: string,
    orgId: string,
    offset = 0,
    limit = 50
  ): Promise<{ rows: CustomerAttributes[]; count: number }> {
    const filter: WhereOptions<CustomerAttributes> = {
      orgId,
      name: { [Op.iLike]: `%${customerName}%` },
    };

    const customers = await Customer.findAndCountAll({ where: filter, offset, limit });

    // if (customers.count === 0) throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);

    return { rows: customers.rows.map((customer) => customer.toJSON()), count: customers.count };
  }

  public async searchAndGetCustomerIDs(customerName: string, orgId?: string): Promise<string[]> {
    const filter: any = {
      where: {
        [Op.or]: [{ name: { [Op.iLike]: `%${customerName}%` } }],
      },
      attributes: ['id'],
    };
    if (orgId) {
      filter.where['orgId'] = orgId;
    }

    const customers: Customer[] = await Customer.findAll(filter);

    const customerIds: string[] = customers.map((customer) => customer.id);
    return customerIds;
  }

  public async deleteCustomer(id: string): Promise<any> {
    await Customer.destroy({ where: { id } });
  }
}
