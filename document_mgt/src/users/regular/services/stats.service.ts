import { Op } from 'sequelize';
import Document from '../../../models/documents.model';
import InvoiceUtils from './utils/invoice-service.utils';
import StatsUtils from './utils/stats-service.utils';
import CustomerUtils from './utils/customer-service.utils';
import { GrossingInvoiceType, StatType } from '../../../types/document.types';
import moment from 'moment';
import { DOCUMENT_TYPE } from '../../../constants/values.constants';
import { truncateToTwoDecimals } from '../../../utilities/global.utilities';
import { Request } from 'express';
import { ErrorWrapper } from '../../../helpers/class.helpers';

export default class StatServices extends ErrorWrapper {
  public async getStats(
    req: Request,
    filter: string,
    orgId: string
  ): Promise<Record<string, Record<string, number>>> {
    const { prevTimeFrame, currentTimeFrame } = StatsUtils.getTimeFrame(filter, req);
    const obj: Record<string, Record<string, any>> = {
      stats: {
        netInvoice: 0,
        invoices: 0,
        receipt: 0,
        creditnote: 0,
        documents: 0,
        netInvoicePercentageChange: 0,
        invoicePercentageChange: 0,
        receiptPercentageChange: 0,
        creditnotePercentageChange: 0,
        documentPercentageChange: 0,
        intervals: [],
      },
    };

    const documentStats = await this.getDocumentStats(
      req,
      orgId,
      prevTimeFrame,
      currentTimeFrame,
      filter
    );

    obj.stats.netInvoice = documentStats.netInvoice;
    obj.stats.invoices = documentStats.invoices;
    obj.stats.receipt = documentStats.receipt;
    obj.stats.documents = documentStats.documents;
    obj.stats.creditnote = documentStats.creditnote;
    obj.stats.intervals = documentStats.data;
    obj.stats.netInvoicePercentageChange = documentStats.netInvoicePercentageChange;
    obj.stats.invoicePercentageChange = documentStats.invoicePercentageChange;
    obj.stats.receiptPercentageChange = documentStats.receiptPercentageChange;
    obj.stats.creditnotePercentageChange = documentStats.creditnotePercentageChange;
    obj.stats.documentPercentageChange = documentStats.documentPercentageChange;

    return obj;
  }

  public async getAdminStats(req: Request, filter: string): Promise<StatType> {
    const { currentTimeFrame: timeFrame } = StatsUtils.getTimeFrame(filter, req);
    const stats: StatType = await this.getAdminDocumentStats(timeFrame);
    return stats;
  }

  private async getPeriodicDocStats(
    filter,
    orgId: string,
    startDate: Date | string,
    endDate: Date | string,
    userTimezone: string
  ) {
    let result;
    let invoice;
    let creditnote;
    let receipt;
    switch (filter) {
      case 'daily': {
        result = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `date_part('hour', "createdAt" AT TIME ZONE '${userTimezone}')`
              ),
              'hour',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['hour'],
          order: [['hour', 'ASC']],
        });

        invoice = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `date_part('hour', "createdAt" AT TIME ZONE '${userTimezone}')`
              ),
              'hour',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.INVOICE,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['hour'],
          order: [['hour', 'ASC']],
        });

        creditnote = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `date_part('hour', "createdAt" AT TIME ZONE '${userTimezone}')`
              ),
              'hour',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.CREDIT_NOTE,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['hour'],
          order: [['hour', 'ASC']],
        });

        receipt = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `date_part('hour', "createdAt" AT TIME ZONE '${userTimezone}')`
              ),
              'hour',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('amountPaid')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.RECEIPT,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['hour'],
          order: [['hour', 'ASC']],
        });

        const startMoment = moment(startDate).startOf('hour');
        const hourlyData = Array.from({ length: 24 }, (_, i) => ({
          period: startMoment.clone().add(i, 'hours').format('h:mm A'),
          invoiceAmount: 0,
          creditnoteAmount: 0,
          receiptAmount: 0,
          totalDocuments: 0,
          totalInvoice: 0,
          totalCreditnote: 0,
          totalReceipt: 0,
        }));

        invoice.forEach((row) => {
          const dataHour = parseInt(row.dataValues.hour, 10);
          let hourIndex = dataHour - startMoment.hour();
          if (hourIndex < 0) hourIndex += 24;

          hourlyData[hourIndex].invoiceAmount = Number(row.dataValues.totalAmount);
          hourlyData[hourIndex].totalInvoice = Number(row.dataValues.totalDocuments);
        });

        creditnote.forEach((row) => {
          const dataHour = parseInt(row.dataValues.hour, 10);
          let hourIndex = dataHour - startMoment.hour();
          if (hourIndex < 0) hourIndex += 24;

          hourlyData[hourIndex].creditnoteAmount = Number(row.dataValues.totalAmount);
          hourlyData[hourIndex].totalCreditnote = Number(row.dataValues.totalDocuments);
        });

        receipt.forEach((row) => {
          const dataHour = parseInt(row.dataValues.hour, 10);
          let hourIndex = dataHour - startMoment.hour();
          if (hourIndex < 0) hourIndex += 24;
          hourlyData[hourIndex].receiptAmount = Number(row.dataValues.totalAmount);
          hourlyData[hourIndex].totalReceipt = Number(row.dataValues.totalDocuments);
        });

        result.forEach((row) => {
          const dataHour = parseInt(row.dataValues.hour, 10);
          let hourIndex = dataHour - startMoment.hour();
          if (hourIndex < 0) hourIndex += 24;
          hourlyData[hourIndex].totalDocuments = Number(row.dataValues.totalDocuments);
          hourlyData[hourIndex].invoiceAmount = hourlyData[hourIndex].invoiceAmount || 0;
          hourlyData[hourIndex].creditnoteAmount = hourlyData[hourIndex].creditnoteAmount || 0;
          hourlyData[hourIndex].receiptAmount = hourlyData[hourIndex].receiptAmount || 0;
          hourlyData[hourIndex].totalInvoice = hourlyData[hourIndex].totalInvoice || 0;
          hourlyData[hourIndex].totalCreditnote = hourlyData[hourIndex].totalCreditnote || 0;
          hourlyData[hourIndex].totalReceipt = hourlyData[hourIndex].totalReceipt || 0;
        });
        result = hourlyData;
        break;
      }
      case 'weekly': {
        result = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `to_char("createdAt" AT TIME ZONE '${userTimezone}', 'Mon FMDD')`
              ),
              'date',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['date'],
          order: [['date', 'ASC']],
        });

        invoice = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `to_char("createdAt" AT TIME ZONE '${userTimezone}', 'Mon FMDD')`
              ),
              'date',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.INVOICE,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['date'],
          order: [['date', 'ASC']],
        });

        creditnote = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `to_char("createdAt" AT TIME ZONE '${userTimezone}', 'Mon FMDD')`
              ),
              'date',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.CREDIT_NOTE,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['date'],
          order: [['date', 'ASC']],
        });
        receipt = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `to_char("createdAt" AT TIME ZONE '${userTimezone}', 'Mon FMDD')`
              ),
              'date',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('amountPaid')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.RECEIPT,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['date'],
          order: [['date', 'ASC']],
        });

        const weekStartMoment = moment(startDate).startOf('day');

        const endMoment = moment(endDate).endOf('day');

        const daysDiff = endMoment.diff(weekStartMoment, 'days');

        const weeklyData = Array.from({ length: daysDiff + 1 }, (_, i) => ({
          period: weekStartMoment.clone().add(i, 'days').format('MMM D'),

          invoiceAmount: 0,
          creditnoteAmount: 0,
          receiptAmount: 0,
          totalDocuments: 0,
          totalInvoice: 0,
          totalCreditnote: 0,
          totalReceipt: 0,
        }));

        result = result.map((row: any) => ({
          period: row.dataValues.date,
          totalAmount: truncateToTwoDecimals(row.dataValues.totalAmount),
          totalDocuments: Number(row.dataValues.totalDocuments),
        }));

        invoice.forEach((row) => {
          const dateIndex = weeklyData.findIndex((day) => day.period === row.dataValues.date);
          if (dateIndex !== -1) weeklyData[dateIndex].invoiceAmount = Number(row.totalAmount);
          if (dateIndex !== -1)
            weeklyData[dateIndex].totalInvoice = Number(row.dataValues.totalDocuments);
        });

        creditnote.forEach((row) => {
          const dateIndex = weeklyData.findIndex((day) => day.period === row.dataValues.date);
          if (dateIndex !== -1) weeklyData[dateIndex].creditnoteAmount = Number(row.totalAmount);
          if (dateIndex !== -1)
            weeklyData[dateIndex].totalCreditnote = Number(row.dataValues.totalDocuments);
        });

        receipt.forEach((row) => {
          const dateIndex = weeklyData.findIndex((day) => day.period === row.dataValues.date);
          if (dateIndex !== -1) weeklyData[dateIndex].receiptAmount = Number(row.totalAmount);
          if (dateIndex !== -1)
            weeklyData[dateIndex].totalReceipt = Number(row.dataValues.totalDocuments);
        });

        result.forEach((row) => {
          const dateIndex = weeklyData.findIndex((day) => day.period === row.period);
          if (dateIndex !== -1) {
            weeklyData[dateIndex] = {
              period: row.period,
              invoiceAmount: weeklyData[dateIndex].invoiceAmount || 0,
              creditnoteAmount: weeklyData[dateIndex].creditnoteAmount || 0,
              receiptAmount: weeklyData[dateIndex].receiptAmount || 0,
              totalInvoice: weeklyData[dateIndex].totalInvoice || 0,
              totalCreditnote: weeklyData[dateIndex].totalCreditnote || 0,
              totalReceipt: weeklyData[dateIndex].totalReceipt || 0,
              totalDocuments: Number(row.totalDocuments),
            };
          }
        });
        result = weeklyData;
        break;
      }
      case 'monthly': {
        result = await Document.findAll({
          attributes: [
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
            [
              Document.sequelize.literal(
                `date_trunc('week', "createdAt"  AT TIME ZONE '${userTimezone}')`
              ),
              'week',
            ],
          ],
          where: {
            orgId,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: 'week',
          order: [['week', 'ASC']],
        });

        invoice = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `date_trunc('week', "createdAt"  AT TIME ZONE '${userTimezone}')`
              ),
              'week',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.INVOICE,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: 'week',
          order: [['week', 'ASC']],
        });

        creditnote = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `date_trunc('week', "createdAt"  AT TIME ZONE '${userTimezone}')`
              ),
              'week',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.CREDIT_NOTE,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: 'week',
          order: [['week', 'ASC']],
        });

        receipt = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `date_trunc('week', "createdAt"  AT TIME ZONE '${userTimezone}')`
              ),
              'week',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('amountPaid')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.RECEIPT,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: 'week',
          order: [['week', 'ASC']],
        });
        result = result.map((row: any) => {
          const weekStart = moment(row.dataValues.week).startOf('week');

          const weekEnd = weekStart.clone().endOf('week');

          return {
            period: `${weekStart.format('MMM D')} - ${weekEnd.format('MMM D')}`,
            totalAmount: truncateToTwoDecimals(row.dataValues.totalAmount),
            totalDocuments: Number(row.dataValues.totalDocuments),
          };
        });
        const monthStartMoment = moment(startDate).startOf('week');

        const monthEndMoment = moment(endDate).endOf('week');

        const totalWeeks = monthEndMoment.diff(monthStartMoment, 'weeks');

        const monthData = Array.from({ length: totalWeeks + 1 }, (_, i) => {
          const weekStart = monthStartMoment.clone().add(i, 'weeks');

          const weekEnd = weekStart.clone().endOf('week');

          return {
            period: `${weekStart.format('MMM D')} - ${weekEnd.format('MMM D')}`,

            invoiceAmount: 0,
            creditnoteAmount: 0,
            receiptAmount: 0,
            totalDocuments: 0,
            totalInvoice: 0,
            totalCreditnote: 0,
            totalReceipt: 0,
          };
        });

        invoice.forEach((row) => {
          const weekStart = moment(row.dataValues.week).startOf('week');

          const weekEnd = weekStart.clone().endOf('week');

          const period = `${weekStart.format('MMM D')} - ${weekEnd.format('MMM D')}`;
          const monthIndex = monthData.findIndex((week) => week.period === period);
          if (monthIndex !== -1) monthData[monthIndex].invoiceAmount = Number(row.totalAmount);
          if (monthIndex !== -1)
            monthData[monthIndex].totalInvoice = Number(row.dataValues.totalDocuments);
        });

        creditnote.forEach((row) => {
          const weekStart = moment(row.dataValues.week).startOf('week');

          const weekEnd = weekStart.clone().endOf('week');

          const period = `${weekStart.format('MMM D')} - ${weekEnd.format('MMM D')}`;
          const monthIndex = monthData.findIndex((week) => week.period === period);
          if (monthIndex !== -1) monthData[monthIndex].creditnoteAmount = Number(row.totalAmount);
          if (monthIndex !== -1)
            monthData[monthIndex].totalCreditnote = Number(row.dataValues.totalDocuments);
        });

        receipt.forEach((row) => {
          const weekStart = moment(row.dataValues.week).startOf('week');

          const weekEnd = weekStart.clone().endOf('week');

          const period = `${weekStart.format('MMM D')} - ${weekEnd.format('MMM D')}`;
          const monthIndex = monthData.findIndex((week) => week.period === period);
          if (monthIndex !== -1) monthData[monthIndex].receiptAmount = Number(row.totalAmount);
          if (monthIndex !== -1)
            monthData[monthIndex].totalReceipt = Number(row.dataValues.totalDocuments);
        });

        result.forEach((row) => {
          const monthIndex = monthData.findIndex((week) => week.period === row.period);
          if (monthIndex !== -1) {
            monthData[monthIndex] = {
              period: row.period,
              invoiceAmount: monthData[monthIndex].invoiceAmount || 0,
              creditnoteAmount: monthData[monthIndex].creditnoteAmount || 0,
              receiptAmount: monthData[monthIndex].receiptAmount || 0,
              totalCreditnote: monthData[monthIndex].totalCreditnote || 0,
              totalInvoice: monthData[monthIndex].totalInvoice || 0,
              totalReceipt: monthData[monthIndex].totalReceipt || 0,
              totalDocuments: Number(row.totalDocuments),
            };
          }
        });

        result = monthData;
        break;
      }
      case 'annual': {
        result = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `to_char("createdAt" AT TIME ZONE '${userTimezone}', 'Mon YYYY')`
              ),
              'month',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['month'],
          order: [['month', 'ASC']],
        });
        invoice = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `to_char("createdAt" AT TIME ZONE '${userTimezone}', 'Mon YYYY')`
              ),
              'month',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.INVOICE,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['month'],
          order: [['month', 'ASC']],
        });
        receipt = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `to_char("createdAt" AT TIME ZONE '${userTimezone}', 'Mon YYYY')`
              ),
              'month',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.RECEIPT,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['month'],
          order: [['month', 'ASC']],
        });
        creditnote = await Document.findAll({
          attributes: [
            [
              Document.sequelize.literal(
                `to_char("createdAt" AT TIME ZONE '${userTimezone}', 'Mon YYYY')`
              ),
              'month',
            ],
            [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'totalAmount'],
            [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
          ],
          where: {
            orgId,
            type: DOCUMENT_TYPE.CREDIT_NOTE,
            createdAt: {
              [Op.between]: [startDate, endDate],
            },
            status: {
              [Op.ne]: 'draft',
            },
          },
          group: ['month'],
          order: [['month', 'ASC']],
        });
        result = result.map((row: any) => ({
          period: row.dataValues.month.trim(),
          totalAmount: Number(row.dataValues.totalAmount),
          totalDocuments: Number(row.dataValues.totalDocuments),
        }));

        const annualStartMoment = moment(startDate).startOf('month');

        const annualEndMoment = moment(endDate).endOf('month');

        const totalMonths = annualEndMoment.diff(annualStartMoment, 'months');

        const monthlyData = Array.from({ length: totalMonths + 1 }, (_, i) => {
          const monthStart = annualStartMoment.clone().add(i, 'months');

          return {
            period: monthStart.format('MMM YYYY'),

            invoiceAmount: 0,
            creditnoteAmount: 0,
            receiptAmount: 0,
            totalDocuments: 0,
            totalInvoice: 0,
            totalCreditnote: 0,
            totalReceipt: 0,
          };
        });

        invoice.forEach((row) => {
          const monthIndex = monthlyData.findIndex(
            (month) => month.period === row.dataValues.month.trim()
          );
          if (monthIndex !== -1)
            monthlyData[monthIndex].invoiceAmount = Number(row.dataValues.totalAmount);
          monthlyData[monthIndex].totalInvoice = Number(row.dataValues.totalDocuments);
        });

        creditnote.forEach((row) => {
          const monthIndex = monthlyData.findIndex(
            (month) => month.period === row.dataValues.month.trim()
          );
          if (monthIndex !== -1)
            monthlyData[monthIndex].creditnoteAmount = Number(row.dataValues.totalAmount);
          monthlyData[monthIndex].totalCreditnote = Number(row.dataValues.totalDocuments);
        });

        receipt.forEach((row) => {
          const monthIndex = monthlyData.findIndex(
            (month) => month.period === row.dataValues.month.trim()
          );
          if (monthIndex !== -1)
            monthlyData[monthIndex].receiptAmount = Number(row.dataValues.totalAmount);
          monthlyData[monthIndex].totalReceipt = Number(row.dataValues.totalDocuments);
        });

        result.forEach((row) => {
          const monthIndex = monthlyData.findIndex((month) => month.period === row.period);
          if (monthIndex !== -1) {
            monthlyData[monthIndex] = {
              period: row.period,
              invoiceAmount: monthlyData[monthIndex].invoiceAmount || 0,
              creditnoteAmount: monthlyData[monthIndex].creditnoteAmount || 0,
              receiptAmount: monthlyData[monthIndex].receiptAmount || 0,
              totalInvoice: monthlyData[monthIndex].totalInvoice || 0,
              totalCreditnote: monthlyData[monthIndex].totalCreditnote || 0,
              totalReceipt: monthlyData[monthIndex].totalReceipt || 0,
              totalDocuments: Number(row.totalDocuments),
            };
          }
        });

        result = monthlyData;
        break;
      }
      default:
        return;
    }
    return result;
  }

  private async getDocumentStats(
    req: Request,
    orgId: string,
    prevTimeFrame: [Date, Date],
    currentTimeFrame: [Date, Date],
    filter: string
  ): Promise<{ [key: string]: number | Document[] }> {
    const [currentStartDate, currentEndDate] = currentTimeFrame;
    const [prevStartDate, prevEndDate] = prevTimeFrame;

    const documentStats: Record<string, any> = {
      netInvoice: 0,
      invoices: 0,
      receipt: 0,
      creditnote: 0,
      documents: 0,
      netInvoicePercentageChange: 0,
      invoicePercentageChange: 0,
      receiptPercentageChange: 0,
      creditnotePercentageChange: 0,
      documentPercentageChange: 0,
      data: [],
    };

    const currentTotalDocumentAmount: Record<string, any> = {
      netInvoice: 0,
      invoice: 0,
      receipt: 0,
      creditnote: 0,
      documents: 0,
    };

    const previousTotalDocumentAmount: Record<string, any> = {
      netInvoice: 0,
      invoice: 0,
      receipt: 0,
      creditnote: 0,
      documents: 0,
    };

    const stats: Document[] = await Document.findAll({
      attributes: [
        'type',
        [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'total_Amount'],
        [Document.sequelize.fn('sum', Document.sequelize.col('amountPaid')), 'amount_paid'],
        [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
      ],
      where: {
        orgId,
        createdAt: { [Op.between]: [currentStartDate, currentEndDate] },
        status: {
          [Op.ne]: 'draft',
        },
      },
      group: ['type'],
    });

    const prevStats: Document[] = await Document.findAll({
      attributes: [
        'type',
        [Document.sequelize.fn('sum', Document.sequelize.col('totalAmount')), 'total_Amount'],
        [Document.sequelize.fn('sum', Document.sequelize.col('amountPaid')), 'amount_paid'],
        [Document.sequelize.fn('count', Document.sequelize.col('*')), 'totalDocuments'],
      ],
      where: {
        orgId,
        createdAt: { [Op.between]: [prevStartDate, prevEndDate] },
        status: {
          [Op.ne]: 'draft',
        },
      },
      group: ['type'],
    });

    prevStats.forEach((stat: any) => {
      switch (stat.type) {
        case 'invoice':
          previousTotalDocumentAmount.invoice = truncateToTwoDecimals(stat.get('total_Amount'));
          previousTotalDocumentAmount.documents += Number(stat.get('totalDocuments'));
          break;
        case 'receipt':
          previousTotalDocumentAmount.receipt = truncateToTwoDecimals(stat.get('amount_paid'));
          previousTotalDocumentAmount.documents += Number(stat.get('totalDocuments'));
          break;
        case 'creditnote':
          previousTotalDocumentAmount.creditnote = truncateToTwoDecimals(stat.get('total_Amount'));
          previousTotalDocumentAmount.documents += Number(stat.get('totalDocuments'));
          break;
        default:
          break;
      }
    });

    stats.forEach((stat: any) => {
      switch (stat.type) {
        case 'invoice':
          documentStats.invoices = truncateToTwoDecimals(stat.get('total_Amount'));
          currentTotalDocumentAmount.invoice = truncateToTwoDecimals(stat.get('total_Amount'));
          currentTotalDocumentAmount.documents += Number(stat.get('totalDocuments'));
          break;
        case 'receipt':
          documentStats.receipt = truncateToTwoDecimals(stat.get('amount_paid'));
          currentTotalDocumentAmount.receipt = truncateToTwoDecimals(stat.get('amount_paid'));
          currentTotalDocumentAmount.documents += Number(stat.get('totalDocuments'));
          break;
        case 'creditnote':
          documentStats.creditnote = truncateToTwoDecimals(stat.get('total_Amount'));
          currentTotalDocumentAmount.creditnote = truncateToTwoDecimals(stat.get('total_Amount'));
          currentTotalDocumentAmount.documents += Number(stat.get('totalDocuments'));
          break;
        default:
          break;
      }
    });

    currentTotalDocumentAmount.netInvoice = truncateToTwoDecimals(
      currentTotalDocumentAmount.invoice - currentTotalDocumentAmount.creditnote
    );
    previousTotalDocumentAmount.netInvoice = truncateToTwoDecimals(
      previousTotalDocumentAmount.invoice - previousTotalDocumentAmount.creditnote
    );
    documentStats.netInvoicePercentageChange = StatsUtils.getPercentageChange(
      previousTotalDocumentAmount.netInvoice,
      currentTotalDocumentAmount.netInvoice
    );
    documentStats.invoicePercentageChange = StatsUtils.getPercentageChange(
      previousTotalDocumentAmount.invoice,
      currentTotalDocumentAmount.invoice
    );
    documentStats.receiptPercentageChange = StatsUtils.getPercentageChange(
      previousTotalDocumentAmount.receipt,
      currentTotalDocumentAmount.receipt
    );
    documentStats.creditnotePercentageChange = StatsUtils.getPercentageChange(
      previousTotalDocumentAmount.creditnote,
      currentTotalDocumentAmount.creditnote
    );
    documentStats.netInvoice = truncateToTwoDecimals(
      documentStats.invoices - documentStats.creditnote
    );
    documentStats.documents = currentTotalDocumentAmount.documents;
    documentStats.documentPercentageChange = StatsUtils.getPercentageChange(
      previousTotalDocumentAmount.documents,
      currentTotalDocumentAmount.documents
    );

    documentStats.data = await this.getPeriodicDocStats(
      filter,
      orgId,
      currentStartDate,
      currentEndDate,
      req.userTimezone
    );

    return documentStats;
  }

  private async getAdminDocumentStats(timeFrame: [Date, Date]): Promise<StatType> {
    const stats: StatType = {
      totalCustomers: 0,
      totalInvoice: 0,
      processedPayroll: 0,
      topGrossingInvoice: [],
      topGrossingPayroll: [],
    };
    const topGrossingInvoice = await InvoiceUtils.getTopGrossingInvoice(timeFrame);
    const topOrganizationData: GrossingInvoiceType[] =
      StatsUtils.getTopOrganizationData(topGrossingInvoice);
    stats.totalInvoice = await InvoiceUtils.getTotalInvoiceStat(timeFrame);
    stats.totalCustomers = await CustomerUtils.getTotalCustomerStat(timeFrame);
    stats.topGrossingInvoice = topOrganizationData;
    return stats;
  }
}
