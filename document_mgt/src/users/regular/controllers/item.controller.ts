import { Request, Response } from 'express';
import ItemServices from '../services/items.service';
import { catchAsync } from '../../../utilities/catch-async-error';
import { getResponse, ResponseMeta } from '../../../utilities/responses.utilities';
import { ITEM_MESSAGES } from '../../../constants/responses.constants';
import { pagination } from '../../../utilities/global.utilities';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import { USER_ACTIONS } from '../../../constants/values.constants';

export default class ItemControllers extends RequestHandlerErrorWrapper {
  constructor(private readonly itemServices: ItemServices) {
    super();
  }

  public getItems = catchAsync(async (req: Request, res: Response) => {
    const { orgId } = res.locals;
    const paginate = pagination(req);
    if (!paginate) return;

    const responseData = await this.itemServices.getItemsByOrgId(orgId, paginate);
    const { data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      page: paginate.page,
      limit: paginate.limit,
      totalCount: count,
    };

    return getResponse(res, USER_ACTIONS.getItems, ITEM_MESSAGES.getItems, data, meta);
  });

  public getItem = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { orgId } = res.locals;
    const responseData = await this.itemServices.getItemByOrgId(id, orgId);
    if (responseData) {
      getResponse(res, USER_ACTIONS.getItem, ITEM_MESSAGES.getItem, responseData);
    }
  });
}
