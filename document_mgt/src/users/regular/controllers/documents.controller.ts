import { Request, Response } from 'express';
import Document from '../../../models/documents.model';
import {
  currentTimestamp,
  extractIDocumentNumber,
  getArticle,
  getFileNameWithExtension,
  pagination,
  removeWhiteSpace,
  sendOrganizationNotification,
  trimAndLowerCase,
} from '../../../utilities/global.utilities';
import DocumentServices from '../services/documents.service';
import ItemServices from '../services/items.service';
import CustomerServices from '../services/customer.service';
import DocumentUtils from '../services/utils/documents-service.utils';
import {
  DOCUMENT_ENTITY_TYPE,
  DOCUMENT_ENTITY_TYPE_ARRAY,
  DOCUMENT_STATUS,
  DOCUMENT_STATUS_ARRAY,
  DOCUMENT_TYPE,
  DOCUMENT_TYPE_ARRAY,
  FILE_EXTENSION,
  PAYSLIP_BASE_URL,
  USER_ACTIONS,
} from '../../../constants/values.constants';
import {
  DOCUMENTS,
  INVOICES,
  RECEIPTS,
  CREDIT_NOTES,
} from '../../../constants/notification.constants';
import {
  deleteResponse,
  getResponse,
  postResponse,
  ResponseMeta,
} from '../../../utilities/responses.utilities';
import {
  isAllowedArchiveDocument,
  isNotAcceptedDocument,
  isNotAcceptedEntity,
  isNotAcceptedStatus,
  isProductEntity,
  isServiceEntity,
  isSuperAdmin,
} from '../../../utilities/guards';
import { Errors } from '../../../constants/errors.constants';
import { DOCUMENT_MESSAGES } from '../../../constants/responses.constants';
import ServiceServices from '../services/service.service';
import CreditNoteUtils from '../services/utils/credit-note-service.utils.';
import Items from '../../../models/item.model';
import Services from '../../../models/service.model';
import {
  ISendPDFDocumentData,
  ISendPDFDocumentMetaData,
} from '../../../interfaces/documents.interfaces';
import {
  processPDFDocumentData,
  processPDFDocumentMetaData,
  // sendInvoiceReminderNotification,
} from '../../../helpers/documents.helpers';
import { EMAIL_SUBJECTS } from '../../../constants/email.constants';
import Invoice from '../../../models/invoice.models';
import { getUserByOrgId } from '../../../api/user';
import {
  BadRequestError,
  ConflictError,
  NotFoundError,
} from '../../../middlewares/error_handlers/app-error';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import sequelize from '../../../config/database/connection';
import { SendInvoiceReminderPayload } from '../../../interfaces/request-body/document-payload.interfaces';
import { DocumentTypes } from '../../../types/document.types';

export default class DocumentController extends RequestHandlerErrorWrapper {
  constructor(
    private readonly documentServices: DocumentServices,
    private readonly itemService: ItemServices,
    private readonly serviceService: ServiceServices,
    private readonly customerService: CustomerServices
  ) {
    super();
  }

  async createInvoice(req: Request, res: Response) {
    const requestPayload = req.body;

    requestPayload.type = DOCUMENT_TYPE.INVOICE;
    requestPayload.dateIssued = requestPayload.dateIssued ?? null;
    requestPayload.dueDate = requestPayload.dueDate ?? null;
    requestPayload.status = requestPayload.draft
      ? DOCUMENT_STATUS.DRAFT
      : DOCUMENT_STATUS.AWAITING_PAYMENT;

    if (isNotAcceptedEntity(requestPayload.entityType, DOCUMENT_ENTITY_TYPE_ARRAY)) {
      throw new BadRequestError(Errors.INVALID_ENTITY);
    }

    // if (requestPayload.type !== DOCUMENT_TYPE.INVOICE) {
    //   throw new NotFoundError(Errors.DOCUMENT_WITH_INVOICE_ID_NOT_FOUND);
    // }

    const responseData = await this.documentServices.createInvoice(requestPayload);

    if (responseData) {
      if (responseData.status !== DOCUMENT_STATUS.DRAFT) {
        await sendOrganizationNotification(
          res,
          'Invoice created',
          `Invoice: ${responseData.documentNumber} was created for ${responseData.customerName}`,
          'MEDIUM',
          [],
          INVOICES
        );
      }

      DocumentUtils.removeUnwantedDocumentData(req.user.role, responseData, DOCUMENT_TYPE.INVOICE);

      return postResponse(
        res,
        USER_ACTIONS.createInvoice,
        DOCUMENT_MESSAGES.createInvoice,
        responseData
      );
    }
  }

  async createReceipt(req: Request, res: Response) {
    let orgId: string;

    if (req.query.org) {
      const user = await getUserByOrgId(String(req.query.org as string));

      if (!user) throw new NotFoundError(Errors.NO_USER);
    } else {
      orgId = req.user.organization.id;
    }

    req.body.orgId = orgId;
    const receiptPayload = req.body;

    const responseData = await this.documentServices.createReceipt(receiptPayload);

    await sendOrganizationNotification(
      res,
      'Receipt created',
      `Receipt: ${responseData.documentNumber} was created for invoice: ${responseData.invoiceNumber}`,
      'MEDIUM',
      [],
      RECEIPTS
    );

    DocumentUtils.removeUnwantedDocumentData(req.user.role, responseData, DOCUMENT_TYPE.RECEIPT);

    return postResponse(
      res,
      USER_ACTIONS.createReceipt,
      DOCUMENT_MESSAGES.createReceipt,
      responseData
    );
  }

  async createCreditNote(req: Request, res: Response) {
    let orgId: string;

    if (req.query.org) {
      const user = await getUserByOrgId(String(req.query.org as string));

      if (!user) throw new NotFoundError(Errors.NO_USER);
    } else {
      orgId = req.user.organization.id;
    }

    const requestPayload = req.body;

    if (isNotAcceptedEntity(requestPayload.entityType, DOCUMENT_ENTITY_TYPE_ARRAY)) {
      throw new BadRequestError(Errors.INVALID_STATUS);
    }

    const invoiceDocument = await this.documentServices.getDocumentByInvoice(
      requestPayload.invoiceId,
      orgId
    );

    if (!invoiceDocument) return;

    if (invoiceDocument.status === DOCUMENT_STATUS.DRAFT)
      throw new BadRequestError(Errors.INVALID_DOCUMENT);

    const ids: string[] = await DocumentUtils.extractIds(requestPayload);

    if (isProductEntity(requestPayload.entityType)) {
      const foundItems = await DocumentUtils.getModelIds(Items, ids, orgId, invoiceDocument.docId);

      if (!foundItems) return;

      for (const entity of requestPayload.items) {
        const item = foundItems.find((item) => item.get('id') === entity.id);
        entity.vat = item.get('vat');
        entity.discount = item.get('discount');
        entity.name = item.get('name');
        entity.description = item.get('description');
      }
    }

    if (isServiceEntity(requestPayload.entityType)) {
      const foundServices = await DocumentUtils.getModelIds(
        Services,
        ids,
        orgId,
        invoiceDocument.docId
      );

      if (!foundServices) return;

      for (const entity of requestPayload.services) {
        const service = foundServices.find((service) => service.get('id') === entity.id);
        entity.vat = service.get('vat');
        entity.type = service.get('type');
        entity.description = service.get('description');
        entity.discount = service.get('discount');
      }
    }

    const documentData = isProductEntity(requestPayload.entityType)
      ? await DocumentUtils.updateDocumentItems(requestPayload)
      : await DocumentUtils.updateDocumentServices(requestPayload);

    if (!documentData) return;

    const responseData = await this.documentServices.createCreditNote(
      documentData,
      invoiceDocument
    );

    if (responseData.status !== DOCUMENT_STATUS.DRAFT) {
      await sendOrganizationNotification(
        res,
        'Credit note created',
        `Credit note: ${responseData.documentNumber} was created for invoice: ${invoiceDocument.documentNumber}`,
        'MEDIUM',
        [],
        CREDIT_NOTES
      );
    }

    DocumentUtils.removeUnwantedDocumentData(
      req.user.role,
      responseData,
      DOCUMENT_TYPE.CREDIT_NOTE
    );
    postResponse(
      res,
      USER_ACTIONS.createCreditNote,
      DOCUMENT_MESSAGES.createCreditNote,
      responseData
    );
  }

  async processSystemInvoiceDocument(req: Request, res: Response) {
    const timestamp = currentTimestamp(req);
    const { customer, orgId, ...invoiceDTO } = req.body;
    const customerPayload = { ...customer, createdAt: timestamp, updatedAt: timestamp, orgId };

    if (invoiceDTO.type !== DOCUMENT_TYPE.INVOICE)
      throw new NotFoundError(Errors.DOCUMENT_WITH_INVOICE_ID_NOT_FOUND);

    const customerData = await this.customerService.createACustomer(customerPayload);
    if (!customerData) return;
    const invoicePayload = { ...invoiceDTO, orgId, customerId: customerData.id };
    const invoiceData = await this.documentServices.createInvoice(invoicePayload);
    if (!invoiceData) return;
    getResponse(res, USER_ACTIONS.processSystemInvoiceDocument, DOCUMENT_MESSAGES.createInvoice, {
      invoice_id: invoiceData.documentNumber,
    });
  }

  async processSystemReceiptDocument(req: Request, res: Response) {
    const timestamp = currentTimestamp(req);
    const {
      invoiceId,
      amountPaid,
      terms,
      orgId,
      datePaid,
      expiryDate,
      reference,
      paymentMethod,
      currency,
    } = req.body;
    const doc = await Document.findOne({ where: { documentNumber: invoiceId, orgId } });
    const invoice = await Invoice.findOne({ where: { invoiceNumber: invoiceId, orgId } });

    if (doc.type !== DOCUMENT_TYPE.INVOICE)
      throw new NotFoundError(Errors.DOCUMENT_WITH_INVOICE_ID_NOT_FOUND);

    const customerData = await this.customerService.getCustomer(doc.customerId, doc.orgId);
    let entityData: Services[] | Items[];
    if (doc.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT)
      entityData = await this.itemService.getItemsByDocId(doc.id, doc.orgId);
    if (doc.entityType === DOCUMENT_ENTITY_TYPE.SERVICE)
      entityData = await this.serviceService.getServicesByDocId(doc.id, doc.orgId);
    entityData = entityData.map((data: any) => {
      return { ...data.dataValues };
    });
    const user = (await getUserByOrgId(doc.orgId)).find((res) => res.default === true);
    const receiptPayload = {
      orgId,
      invoiceId: invoice.id,
      amountPaid,
      createdAt: timestamp,
      updatedAt: timestamp,
    };
    let entities: any;
    if (doc.entityType === DOCUMENT_ENTITY_TYPE.SERVICE) entities = { services: entityData };
    if (doc.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT) entities = { items: entityData };
    const receiptData = await this.documentServices.createReceipt(receiptPayload);
    if (!receiptData) return;
    let meta: ISendPDFDocumentMetaData = processPDFDocumentMetaData(
      {
        entityType: doc.entityType,
        ...entities,
        reference,
        paymentMethod,
        dateIssued: timestamp,
        datePaid,
        type: doc.type,
        amountPaid,
      },
      req.userTimezone,
      true
    );
    meta.currency = currency;
    meta.paymentLink = `${PAYSLIP_BASE_URL}/payslip-generator/manage-payments`;
    if (doc.entityType === DOCUMENT_ENTITY_TYPE.SERVICE)
      meta = { ...meta, terms, services: entityData, datePaid, expiryDate };
    if (doc.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT)
      meta = { ...meta, terms, items: entityData, datePaid, expiryDate };

    const pdfDocumentData: ISendPDFDocumentData = processPDFDocumentData(
      receiptData,
      customerData,
      user,
      EMAIL_SUBJECTS.subscriptionPayment,
      meta
    );
    await this.documentServices.convertDocumentToPDFAndSendEmail(
      res,
      pdfDocumentData,
      customerData.email,
      user,
      'payment',
      req.userTimezone
    );
    getResponse(res, USER_ACTIONS.processSystemReceiptDocument, DOCUMENT_MESSAGES.sendDocument);
  }

  async processSystemPaymentDocument(req: Request, res: Response) {
    // collect the document data
    const timestamp = currentTimestamp(req);
    const { customer, currency, orgId, paid, amountPaid, terms, ...invoiceDTO } = req.body;
    // clean data to have all the details for tne invoicing
    const customerPayload = { ...customer, createdAt: timestamp, updatedAt: timestamp, orgId };
    const user = await getUserByOrgId(orgId);
    if (invoiceDTO.type !== DOCUMENT_TYPE.INVOICE)
      throw new NotFoundError(Errors.DOCUMENT_WITH_INVOICE_ID_NOT_FOUND);
    const customerData = await this.customerService.createACustomer(customerPayload);
    if (!customerData) return;
    const invoicePayload = { ...invoiceDTO, orgId, customerId: customerData.id };
    // create invoicing
    // check if invoice has been created
    const invoiceData = await this.documentServices.createInvoice(invoicePayload);
    if (!invoiceData) return;
    // document is  paid, create receipt and send notification
    if (paid) {
      const timestamp = currentTimestamp(req);
      const receiptPayload = {
        orgId,
        invoiceId: invoiceData.invoiceId,
        amountPaid,
        createdAt: timestamp,
        updatedAt: timestamp,
      };
      const receiptData = await this.documentServices.createReceipt(receiptPayload);
      if (!receiptData) return;
      let meta: ISendPDFDocumentMetaData = processPDFDocumentMetaData(
        req.body,
        req.userTimezone,
        true
      );
      meta.currency = currency;
      if (invoiceDTO.entityType === DOCUMENT_ENTITY_TYPE.SERVICE)
        meta = { ...meta, terms, services: invoiceDTO.services };
      if (invoiceDTO.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT)
        meta = { ...meta, terms, items: invoiceDTO.items };

      const pdfDocumentData: ISendPDFDocumentData = processPDFDocumentData(
        receiptData,
        customerData,
        user,
        EMAIL_SUBJECTS.subscriptionPayment,
        meta
      );
      await this.documentServices.convertDocumentToPDFAndSendEmail(
        res,
        pdfDocumentData,
        customerData.email,
        user,
        'payment',
        req.userTimezone
      );
    }

    getResponse(res, USER_ACTIONS.processSystemPaymentDocument, DOCUMENT_MESSAGES.sendDocument);
  }

  async updateDocument(req: Request, res: Response) {
    const id = req.params.id;
    const requestPayload: Record<string, any> = req.body;
    const { orgId } = res.locals;

    const document = await this.documentServices.getDraftDocument(orgId, id);
    if (!document) return;

    if (document.archive) throw new ConflictError(Errors.DOCUMENT_IS_ARCHIVED);

    if (requestPayload.entityType !== document.entityType)
      throw new BadRequestError(Errors.INVALID_ENTITY);

    requestPayload.type = document.type;
    requestPayload.docId = document.docId;
    requestPayload.orgId = orgId;

    if (requestPayload.type === DOCUMENT_TYPE.CREDIT_NOTE) delete requestPayload.dueDate;

    const ids: string[] = await DocumentUtils.extractIds(requestPayload);

    const data = await sequelize.transaction(async (transaction) => {
      let bulkDelete: any;

      if (requestPayload.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT)
        bulkDelete = await this.itemService.bulkDeleteItemByDocId(
          orgId,
          requestPayload.docId,
          ids,
          transaction
        );

      if (requestPayload.entityType === DOCUMENT_ENTITY_TYPE.SERVICE)
        bulkDelete = await this.serviceService.bulkDeleteServiceByDocId(
          orgId,
          requestPayload.docId,
          ids,
          transaction
        );

      if (!bulkDelete) throw new BadRequestError(Errors.DELETE_ITEMS_ERROR);

      await DocumentUtils.processEntityAmountsAndUpdateData(
        requestPayload,
        this.itemService,
        this.serviceService,
        transaction
      );

      let data;

      if (requestPayload.type === DOCUMENT_TYPE.INVOICE) {
        data = await this.documentServices.editInvoice(orgId, document.id, requestPayload);
      }

      if (requestPayload.type === DOCUMENT_TYPE.CREDIT_NOTE) {
        const invoice = await CreditNoteUtils.getInvoiceDocument(orgId, document.documentNumber);
        requestPayload.invoiceId = invoice['Invoice'].id;
        data = await this.documentServices.editCreditNote(orgId, document.id, requestPayload);
      }
      return data;
    });

    const documentType = String(document.type).toLowerCase();

    if (data) {
      await sendOrganizationNotification(
        res,
        'Document updated',
        `${getArticle(documentType)} ${documentType} with document number: ${data.documentNumber} was updated.`,
        'HIGH',
        [],
        DOCUMENTS
      );

      return postResponse(res, USER_ACTIONS.updateDocument, DOCUMENT_MESSAGES.updateDocument, data);
    }
  }

  async getDocuments(req: Request, res: Response) {
    const { orgId } = res.locals;

    const paginate = pagination(req);
    if (!paginate) return;

    const responseData = await this.documentServices.getDocuments(req, orgId, paginate);
    if (!responseData) return;

    const { data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      limit: paginate.limit,
      page: paginate.page,
      totalCount: count,
    };

    return getResponse(res, USER_ACTIONS.getDocuments, DOCUMENT_MESSAGES.getDocuments, data, meta);
  }

  async getAllDocuments(req: Request, res: Response) {
    const { orgId } = res.locals;

    const paginate = pagination(req);
    if (!paginate) return;

    const responseData = await this.documentServices.getAllDocuments(req, orgId, paginate);

    const { data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      page: paginate.page,
      limit: paginate.limit,
      totalCount: count,
    };

    return getResponse(
      res,
      USER_ACTIONS.getAllDocuments,
      DOCUMENT_MESSAGES.getDocuments,
      data,
      meta
    );
  }

  async getLastDocumentRegNumber(req: Request, res: Response) {
    const type = req.params.type.toLowerCase() as DocumentTypes;
    const orgId = req.user.organization.id;

    const responseData = await DocumentUtils.getNextDocumentNumber(orgId, type);
    if (responseData)
      getResponse(res, USER_ACTIONS.getLastDocumentNumber, `Last ${type} returned successfully.`, {
        lastDocumentNumber: responseData,
      });
  }

  async getRecentDocuments(req: Request, res: Response) {
    const { orgId } = res.locals;

    const responseData: Document[] = await this.documentServices.getRecentDocuments(orgId);

    if (responseData)
      getResponse(
        res,
        USER_ACTIONS.getRecentDocuments,
        DOCUMENT_MESSAGES.recentDocuments,
        responseData
      );
  }

  async filterDocument(req: Request, res: Response) {
    const { status, type, archive } = {
      status: req.query.status ? String(req.query.status) : undefined,
      type: req.query.type ? String(req.query.type) : undefined,
      archive: req.query.archive ? Boolean(req.query.archive) : false,
    };
    const { orgId } = res.locals;
    let docType: string;
    let docStatus: string;
    const whereCondition: any = { archive };

    if (type) {
      docType = trimAndLowerCase(type);
      whereCondition.type = docType;
    }
    if (status) {
      docStatus = trimAndLowerCase(status);
      whereCondition.status = docStatus;
    }

    if (type && isNotAcceptedDocument(docType, DOCUMENT_TYPE_ARRAY)) {
      throw new BadRequestError(Errors.INVALID_DOCUMENT_TYPE);
    }

    if (status && isNotAcceptedStatus(docStatus, DOCUMENT_STATUS_ARRAY)) {
      throw new BadRequestError(Errors.INVALID_STATUS);
    }

    const paginate = pagination(req);
    if (!paginate) return;
    whereCondition.orgId = orgId;

    if (orgId !== req.user.organization.id && isSuperAdmin(req.user.role)) {
      delete whereCondition.archive;
    }
    const responseData = await this.documentServices.getFilteredDocument(
      orgId,
      whereCondition,
      paginate
    );
    if (!responseData) return;

    const { data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      limit: paginate.limit,
      page: paginate.page,
      totalCount: count,
    };

    return getResponse(
      res,
      USER_ACTIONS.filterDocument,
      DOCUMENT_MESSAGES.getDocuments,
      data,
      meta
    );
  }

  async getDocument(req: Request, res: Response) {
    const id: string = req.params.id;
    const { orgId } = res.locals;

    const responseData = await DocumentUtils.getDocumentByID(
      id,
      orgId,
      this.itemService,
      this.serviceService,
      this.customerService
    );

    if (responseData) {
      getResponse(res, USER_ACTIONS.getDocument, DOCUMENT_MESSAGES.getDocuments, responseData);
    }
  }

  async getDocumentByDocu(req: Request, res: Response) {
    const id: string = req.params.id;
    const { orgId } = res.locals;

    const responseData = await DocumentUtils.getDocumentByID(
      id,
      orgId,
      this.itemService,
      this.serviceService,
      this.customerService
    );

    if (responseData) {
      getResponse(
        res,
        USER_ACTIONS.getDocumentByDocumentId,
        DOCUMENT_MESSAGES.getDocuments,
        responseData
      );
    }
  }

  async archiveDocument(req: Request, res: Response) {
    const id: string = req.params.id;
    const payload = { archive: true, updatedAt: currentTimestamp(req) };
    const { orgId } = res.locals;

    const document = await DocumentUtils.getDocumentByID(
      id,
      orgId,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    if (!document) return;
    if (document.archive) throw new ConflictError(Errors.ARCHIVED);

    if (!isAllowedArchiveDocument(document.status))
      throw new BadRequestError(Errors.INVALID_ARCHIVE_DOC_TYPE);

    await Document.update(payload, {
      where: { id: document.id },
      returning: true,
    });

    const documentType = String(document.type).toLowerCase();

    await sendOrganizationNotification(
      res,
      'Document archived',
      `${getArticle(documentType)} ${documentType} with document number: ${document.documentNumber} was archived.`,
      'HIGH',
      [],
      DOCUMENTS
    );

    return getResponse(res, USER_ACTIONS.archiveDocument, DOCUMENT_MESSAGES.archiveDocument);
  }

  async unArchiveDocument(req: Request, res: Response) {
    const id: string = req.params.id;
    const payload = { archive: false, updatedAt: currentTimestamp(req) };
    const { orgId } = res.locals;

    const document = await DocumentUtils.getDocumentByID(
      id,
      orgId,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    if (!document) return;
    if (!document.archive) throw new ConflictError(Errors.DOCUMENT_IS_NOT_ARCHIVED);

    if (!isAllowedArchiveDocument(document.status)) {
      throw new BadRequestError(Errors.INVALID_ARCHIVE_DOC_TYPE);
    }

    await Document.update(payload, {
      where: { id: document.id },
      returning: true,
    });

    const documentType = String(document.type).toLowerCase();

    await sendOrganizationNotification(
      res,
      'Document un-archived',
      `${getArticle(documentType)} ${documentType} with document number: ${document.documentNumber} was un-archived.`,
      'HIGH',
      [],
      DOCUMENTS
    );

    return getResponse(res, USER_ACTIONS.unArchiveDocument, DOCUMENT_MESSAGES.unArchivedDocument);
  }

  async getArchiveDocuments(req: Request, res: Response) {
    const { orgId } = res.locals;

    const paginate = pagination(req);
    if (!paginate) return;

    const { data, count } = await this.documentServices.getArchiveDocuments(paginate, orgId);

    getResponse(
      res,
      USER_ACTIONS.getArchiveDocuments,
      DOCUMENT_MESSAGES.getArchivedDocuments,
      data,
      count
    );
  }

  async deleteDocument(req: Request, res: Response) {
    const id: string = req.params.id;
    const { orgId } = res.locals;

    const document: Document = await DocumentUtils.getDocumentByID(
      id,
      orgId,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    if (!document) throw new NotFoundError(Errors.DOCUMENT_NOT_FOUND);

    const responseData = await this.documentServices.deleteDocument(id, orgId);
    if (!responseData) return;

    const documentType = String(document.type).toLowerCase();

    await sendOrganizationNotification(
      res,
      'Document deleted',
      `${getArticle(documentType)} ${documentType} with document number: ${document.documentNumber} was deleted.`,
      'HIGH',
      [],
      DOCUMENTS
    );

    return deleteResponse(res, USER_ACTIONS.deleteDocument);
  }

  async downloadDocument(req: Request, res: Response) {
    const payload = req.body;

    const company = removeWhiteSpace(payload.businessName);
    const name: string = `${company}-${payload.documentType}-for-${payload.customerName}`;
    const filename = getFileNameWithExtension(name, FILE_EXTENSION.PDF);

    const buffer = await this.documentServices.convertDocumentToPDF(payload);
    if (!buffer) return;

    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    return res.send(buffer).end();
  }

  // async sendPdfDocumentToEmail(req: Request, res: Response) {
  //   const { content, email } = req.body;
  //   const responseData = await this.documentServices.convertDocumentToPDFAndSendEmail(
  //     res,
  //     content,
  //     email,
  //     req.user,
  //     'main',
  //     req.userTimezone
  //   );

  //   if (!responseData) return;

  //   let notificationData: NotificationAttributes;
  //   if (ADMIN_ACCESS.includes(req.user.role) && ADMIN_APP_ORIGINS.includes(req.headers['origin'])) {
  //     const title: string = NOTIFICATION_MESSAGES.adminSendDocument
  //       .replace('<ADMIN>', req.user.firstname)
  //       .replace('<EMAIL>', email);
  //     notificationData = await getAdminNotificationData(
  //       title,
  //       'documents',

  //       ADMINAPP_GLOBAL_ACCESS
  //     );
  //   } else {
  //     const logTitle: string = NOTIFICATION_MESSAGES.adminSendDocument
  //       .replace(
  //         '<ADMIN>',
  //         `${req.user.firstname} ${req.user.lastname} on ${req.user.organization.name}`
  //       )
  //       .replace('<EMAIL>', email);
  //     const documentTypeContent =
  //       content.documentType.toLowerCase() === 'invoice'
  //         ? `An ${content.documentType.toLowerCase()}`
  //         : `A ${content.documentType.toLowerCase()}`;
  //     const message: string = NOTIFICATION_MESSAGES.sendDocument
  //       .replace('DOCUMENT_TYPE', documentTypeContent)
  //       .replace('CUSTOMER_EMAIL_ADDRESS', email);
  //     notificationData = await getNotificationData(req, message, logTitle, 'documents');
  //   }
  //   sendNotification(notificationData);
  //   getResponse(res, USER_ACTIONS.sendPdfDocumentToEmail, DOCUMENT_MESSAGES.sendDocument);
  // }

  async sendPdfDocumentToEmail(req: Request, res: Response) {
    const { email, ...content } = req.body;
    const responseData = await this.documentServices.convertDocumentToPDFAndSendEmail(
      res,
      content,
      email,
      req.user,
      'no-logo',
      req.userTimezone
    );

    if (!responseData) return;

    const documentType = String(content.documentType).toLowerCase();

    await sendOrganizationNotification(
      res,
      'Document sent',
      `${getArticle(documentType)} ${documentType} with document number: ${content.documentNumber} was sent to ${email}`,
      'LOW',
      [],
      DOCUMENTS
    );

    return getResponse(res, USER_ACTIONS._sendPdfDocumentToEmail, DOCUMENT_MESSAGES.sendDocument);
  }

  async sendInvoiceReminder(req: Request, res: Response) {
    const user = req.user;
    const userTimeZone = req.userTimezone;
    const payload = req.body as SendInvoiceReminderPayload;

    await this.documentServices.sendInvoiceReminder(res, user, userTimeZone, payload);

    // await sendInvoiceReminderNotification(req, payload.recipientEmail);

    await sendOrganizationNotification(
      res,
      'Invoice reminder sent',
      `A reminder was sent to ${payload.recipientEmail} for invoice: ${payload.documentNumber}`,
      'LOW',
      [],
      INVOICES
    );

    return getResponse(res, USER_ACTIONS.sendReminder, DOCUMENT_MESSAGES.sendReminder);
  }

  // async sendReminder(req: Request, res: Response) {
  //   const payload: DocumentReminderDataType = req.body.documentData;
  //   const data = req.body.data;

  //   const type = trimAndLowerCase(data.documentType);
  //   if (type === DOCUMENT_TYPE.INVOICE) data.template = EMAIL_TEMPLATES.invoicePayment;
  //   const template = getTemplateName(type, data);
  //   const pdfBuffer = await this.documentServices.convertDocumentToPDF(data);
  //   const company = removeWhiteSpace(data.businessName);
  //   const name: string = `${company}-${data.documentType}`;
  //   const filename: string = getFileName(FILE_EXTENSION.PDF, name);
  //   const templateData: Record<string, string> = {
  //     template,
  //     customerName: data.customerName,
  //     firstname: req.user.firstname,
  //     businessName: data.businessName,
  //     documentNumber: data.documentNumber,
  //     invoiceNumber: data.invoiceNumber,
  //     totalAmount: data.totalAmount,
  //     amountPaid: data.amountPaid,
  //     dateIssued: data.dateIssued,
  //     datePaid: data.datePaid,
  //     currency: data.currency,
  //   };

  //   const html = await renderTemplate(res, templateData, 'no-logo');

  //   const mailData = {
  //     email: payload.recipient,
  //     subject: EMAIL_SUBJECTS.invoiceReminder,
  //     systemEmail: process.env.NO_REPLY_EMAIL_USERNAME,
  //     html,
  //     attachments: {
  //       filename: `${filename}`,
  //       content: pdfBuffer,
  //     },
  //   };

  //   await EmailFeatures.sendEmail(mailData);
  //   // if (!response) return;
  //   let notificationData: NotificationAttributes;
  //   if (ADMIN_ACCESS.includes(req.user.role) && ADMIN_APP_ORIGINS.includes(req.headers['origin'])) {
  //     const title: string = NOTIFICATION_MESSAGES.adminSendReminder
  //       .replace('<ADMIN>', req.user.firstname)
  //       .replace('<EMAIL>', payload.recipient);
  //     notificationData = await getAdminNotificationData(
  //       title,
  //       'documents',

  //       ADMINAPP_GLOBAL_ACCESS
  //     );
  //   } else {
  //     const logTitle: string = NOTIFICATION_MESSAGES.adminSendReminder
  //       .replace(
  //         '<ADMIN>',
  //         `${req.user.firstname} ${req.user.lastname} on ${req.user.organization.name}`
  //       )
  //       .replace('<EMAIL>', payload.recipient);
  //     notificationData = await getNotificationData(
  //       req,
  //       DOCUMENT_MESSAGES.sendReminder,
  //       logTitle,
  //       'documents'
  //     );
  //   }
  //   sendNotification(notificationData);
  //   getResponse(res, USER_ACTIONS.sendReminder, DOCUMENT_MESSAGES.sendReminder);
  // }

  async searchDocument(req: Request, res: Response) {
    const { name, type, docNumber } = {
      name: req.query.customerName ? String(req.query.customerName) : undefined,
      type: req.query.documentType ? String(req.query.documentType) : undefined,
      docNumber: req.query.documentNumber ? String(req.query.documentNumber) : undefined,
    };

    const { orgId } = res.locals;

    const paginate = pagination(req);
    if (!paginate) return;

    const whereCondition: any = { archive: false };

    let documentType: string;
    let customerName: string;
    let documentNumber: string;
    whereCondition.orgId = orgId;

    if (name) {
      customerName = trimAndLowerCase(req.query.customerName as string);

      const customerIds: string[] = await this.customerService.searchAndGetCustomerIDs(
        customerName,
        orgId
      );
      if (customerIds.length < 1) throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);
      whereCondition.customerIds = customerIds;
    }
    if (docNumber) {
      documentNumber = extractIDocumentNumber(req.query.documentNumber as string);
      whereCondition.documentNumber = documentNumber;
    }
    if (type) {
      documentType = trimAndLowerCase(req.query.documentType as string);
      if (isNotAcceptedDocument(documentType, DOCUMENT_TYPE_ARRAY))
        throw new BadRequestError(Errors.INVALID_DOCUMENT_TYPE);
      whereCondition.type = documentType;
    }
    const responseData = await this.documentServices.searchDocument(whereCondition, paginate);

    const { data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      limit: paginate.limit,
      page: paginate.page,
      totalCount: count,
    };

    return getResponse(
      res,
      USER_ACTIONS.searchCustomer,
      DOCUMENT_MESSAGES.getDocuments,
      data,
      meta
    );
  }
}
