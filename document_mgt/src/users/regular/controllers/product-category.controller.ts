import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import ProductCategoryServices from '../services/category.service';
import {
  deleteResponse,
  getResponse,
  postResponse,
  ResponseMeta,
  sendErrorWithData,
} from '../../../utilities/responses.utilities';
import { INVENTORY_MESSAGES } from '../../../constants/responses.constants';
import { getPagination, sendOrganizationNotification } from '../../../utilities/global.utilities';
import { CreateProductCategoryPayload } from '../../../interfaces/request-body/inventory-payload.interfaces';
import { isValuePresent } from '../../../utilities/guards';
import { StatusCodes } from 'http-status-codes';
import { Errors } from '../../../constants/errors.constants';
import { PRODUCT_CATEGORIES } from '../../../constants/notification.constants';

export default class ProductCategoryControllers extends RequestHandlerErrorWrapper {
  constructor(private productCategoryServices: ProductCategoryServices) {
    super();
  }

  async getOneCategory(req: Request, res: Response) {
    const { orgId } = res.locals;
    const categoryId = req.params.id;

    const category = await this.productCategoryServices.getOneCategory(orgId, categoryId);

    return getResponse(
      res,
      'GET_PRODUCT_CATEGORY',
      INVENTORY_MESSAGES.categoriesRetrieved,
      category
    );
  }

  async getAllCategories(req: Request, res: Response) {
    const { orgId } = res.locals;
    const { offset, page, limit } = getPagination(req);

    const categories = await this.productCategoryServices.getAllCategories(orgId, offset, limit);
    const meta: ResponseMeta = {
      count: categories.rows.length,
      page,
      limit,
      totalCount: categories.count,
    };

    return getResponse(
      res,
      'GET_PRODUCT_CATEGORIES',
      INVENTORY_MESSAGES.categoriesRetrieved,
      categories.rows,
      meta
    );
  }

  async createACategory(req: Request, res: Response) {
    const { orgId } = res.locals;
    const payload = req.body as CreateProductCategoryPayload;

    const category = await this.productCategoryServices.createACategory(orgId, payload);

    await sendOrganizationNotification(
      res,
      'Product category created',
      `Category: ${category.name} was created.`,
      'MEDIUM',
      [],
      PRODUCT_CATEGORIES
    );

    return postResponse(
      res,
      'CREATE_PRODUCT_CATEGORY',
      INVENTORY_MESSAGES.categoryCreated,
      category
    );
  }

  async updateACategory(req: Request, res: Response) {
    const { orgId } = res.locals;
    const categoryId = req.params.id;
    const updates = req.body;

    const category = await this.productCategoryServices.updateACategory(orgId, categoryId, updates);

    await sendOrganizationNotification(
      res,
      'Product category updated',
      `Category: ${category.name} was updated.`,
      'HIGH',
      [],
      PRODUCT_CATEGORIES
    );

    return postResponse(
      res,
      'UPDATE_PRODUCT_CATEGORY',
      INVENTORY_MESSAGES.categoryUpdated,
      category
    );
  }

  async deleteACategory(req: Request, res: Response) {
    const { orgId } = res.locals;
    const categoryId = req.params.id;

    const categoryName = await this.productCategoryServices.deleteACategory(orgId, categoryId);

    await sendOrganizationNotification(
      res,
      'Product category deleted',
      `Category: ${categoryName} was deleted.`,
      'HIGH',
      [],
      PRODUCT_CATEGORIES
    );

    return deleteResponse(res, 'DELETE_PRODUCT_CATEGORY');
  }

  async createBulkCategories(req: Request, res: Response) {
    const { orgId } = res.locals;
    const payload = req.body as CreateProductCategoryPayload[];

    const result = await this.productCategoryServices.createBulkCategories(orgId, payload);

    if (isValuePresent(result.skipped)) {
      return sendErrorWithData(
        res,
        StatusCodes.BAD_REQUEST,
        Errors.CANNOT_CREATE_BULK_CATEGORIES,
        result.skipped
      );
    }

    await sendOrganizationNotification(
      res,
      'Product categories created',
      `${result.created.length} product categories was created.`,
      'MEDIUM',
      [],
      PRODUCT_CATEGORIES
    );

    return postResponse(
      res,
      'CREATE_PRODUCT_CATEGORIES',
      INVENTORY_MESSAGES.categoryCreated,
      result.created
    );
  }
}
