import { Request, Response } from 'express';
import Customer from '../../../models/customer.model';
import CustomerServices from '../services/customer.service';
import {
  deleteResponse,
  getResponse,
  postResponse,
  ResponseMeta,
} from '../../../utilities/responses.utilities';
import { Errors } from '../../../constants/errors.constants';
import { CUSTOMER_MESSAGES } from '../../../constants/responses.constants';
import CustomerUtils from '../services/utils/customer-service.utils';
import { USER_ACTIONS } from '../../../constants/values.constants';
import { pagination, sendOrganizationNotification } from '../../../utilities/global.utilities';
import { CUSTOMERS } from '../../../constants/notification.constants';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import { BadRequestError, NotFoundError } from '../../../middlewares/error_handlers/app-error';
import { CustomerPayload } from '../../../interfaces/request-body/document-payload.interfaces';

export default class CustomerControllers extends RequestHandlerErrorWrapper {
  constructor(private readonly customerServices: CustomerServices) {
    super();
  }

  public async createCustomer(req: Request, res: Response) {
    const { role } = req.user;
    const { orgId } = res.locals;

    const payload = req.body.customers as CustomerPayload[];

    const responseData = await this.customerServices.createCustomer(orgId, payload, role);

    await sendOrganizationNotification(
      res,
      'Customer created',
      `${responseData.length} customer(s) created.`,
      'MEDIUM',
      [],
      CUSTOMERS
    );

    return postResponse(res, USER_ACTIONS.createCustomer, CUSTOMER_MESSAGES.created, responseData);
  }

  public async getCustomers(req: Request, res: Response) {
    const { orgId } = res.locals;
    const paginate = pagination(req);
    if (!paginate) return;

    const responseData = await this.customerServices.getCustomers(paginate, orgId);
    const { data, count } = responseData;

    const meta: ResponseMeta = {
      count: data.length,
      page: paginate.page,
      limit: paginate.limit,
      totalCount: count,
    };

    return getResponse(res, USER_ACTIONS.getCustomers, CUSTOMER_MESSAGES.getCustomers, data, meta);
  }

  public async getCustomer(req: Request, res: Response) {
    const { id } = req.params;
    const { role } = req.user;
    const { orgId } = res.locals;

    const responseData = await this.customerServices.getCustomer(id, orgId);
    if (responseData) {
      CustomerUtils.removeUnwantedCustomerData(role, responseData);
      getResponse(res, USER_ACTIONS.getCustomer, CUSTOMER_MESSAGES.getCustomer, responseData);
    } else {
      throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);
    }
  }

  public async searchCustomer(req: Request, res: Response) {
    const customerName: string = req.query.name as string;

    if (!customerName) {
      throw new BadRequestError(Errors.CUSTOMER_NAME_QUERY_REQUIRED);
    }

    const { page, offset, limit } = pagination(req);

    const { orgId } = res.locals;

    const responseData = await this.customerServices.searchCustomer(
      customerName,
      orgId,
      offset,
      limit
    );

    const { rows: data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      limit,
      page,
      totalCount: count,
    };

    return getResponse(
      res,
      USER_ACTIONS.searchCustomer,
      CUSTOMER_MESSAGES.getCustomers,
      data,
      meta
    );
  }

  public async updateCustomer(req: Request, res: Response) {
    const requestPayload: Record<string, any> = req.body;
    const { id } = req.params;
    const { role } = req.user;
    const { orgId } = res.locals;

    const customer = await this.customerServices.getCustomer(id, orgId);

    if (!customer) {
      throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);
    }

    requestPayload.orgId = customer.orgId;
    const responseData = await this.customerServices.updateCustomerById(id, requestPayload);

    if (responseData) {
      await sendOrganizationNotification(
        res,
        'Customer updated',
        `Customer: ${customer.name} was updated.`,
        'HIGH',
        [],
        CUSTOMERS
      );

      CustomerUtils.removeUnwantedCustomerData(role, responseData);
      postResponse(res, USER_ACTIONS.editCustomer, CUSTOMER_MESSAGES.updateCustomer, responseData);
    }
  }

  public async deleteCustomer(req: Request, res: Response) {
    const customer: Customer = await CustomerUtils.getCustomerById(req.params.id);

    if (!customer) {
      throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);
    }

    await this.customerServices.deleteCustomer(req.params.id);

    await sendOrganizationNotification(
      res,
      'Customer deleted',
      `Customer: ${customer.name} was deleted.`,
      'HIGH',
      [],
      CUSTOMERS
    );

    deleteResponse(res, USER_ACTIONS.deleteCustomer);
  }
}
