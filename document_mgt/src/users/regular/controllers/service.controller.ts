import { Request, Response } from 'express';
import ServiceServices from '../services/service.service';
import { getResponse, ResponseMeta } from '../../../utilities/responses.utilities';
import { SERVICE_MESSAGES } from '../../../constants/responses.constants';
import { pagination } from '../../../utilities/global.utilities';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import { USER_ACTIONS } from '../../../constants/values.constants';

export default class ServiceControllers extends RequestHandlerErrorWrapper {
  constructor(private readonly serviceServices: ServiceServices) {
    super();
  }

  async getServices(req: Request, res: Response) {
    const { orgId } = res.locals;
    const paginate = pagination(req);
    if (!paginate) return;
    const responseData = await this.serviceServices.getServicesByOrgId(orgId, paginate);

    const { data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      page: paginate.page,
      limit: paginate.limit,
      totalCount: count,
    };

    return getResponse(res, USER_ACTIONS.getServices, SERVICE_MESSAGES.getService, data, meta);
  }

  async getService(req: Request, res: Response) {
    const { id } = req.params;
    const { orgId } = res.locals;
    const responseData = await this.serviceServices.getServiceByOrgId(id, orgId);
    if (responseData) {
      getResponse(res, USER_ACTIONS.getService, SERVICE_MESSAGES.getService, responseData);
    }
  }
}
