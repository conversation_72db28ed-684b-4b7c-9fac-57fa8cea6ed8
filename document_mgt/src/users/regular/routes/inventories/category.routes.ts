import { Router } from 'express';
import container from '../../../../containers/container.global';
import {
  validateParamsBody,
  validateRequestBody,
} from '../../../../middlewares/validation/global.validators';
import {
  createBulkProductCategorySchema,
  createProductCategorySchema,
  updateProductCategorySchema,
} from '../../../../middlewares/validation/schemas/request-body/inventory.schemas';
import { uuidParamSchema } from '../../../../middlewares/validation/helpers.utils';

const ProductCategoryRouter = Router({ mergeParams: true });
const ProductCategoryControllers = container.resolve('productCategoryControllers');

ProductCategoryRouter.post(
  '/bulk',
  validateRequestBody(createBulkProductCategorySchema),
  ProductCategoryControllers.createBulkCategories.bind(ProductCategoryControllers)
);

ProductCategoryRouter.route('/:id')
  .all(validateParamsBody(uuidParamSchema))
  .get(ProductCategoryControllers.getOneCategory.bind(ProductCategoryControllers))
  .put(
    validateRequestBody(updateProductCategorySchema),
    ProductCategoryControllers.updateACategory.bind(ProductCategoryControllers)
  )
  .delete(ProductCategoryControllers.deleteACategory.bind(ProductCategoryControllers));

ProductCategoryRouter.route('/')
  .get(ProductCategoryControllers.getAllCategories.bind(ProductCategoryControllers))
  .post(
    validateRequestBody(createProductCategorySchema),
    ProductCategoryControllers.createACategory.bind(ProductCategoryControllers)
  );

export default ProductCategoryRouter;
