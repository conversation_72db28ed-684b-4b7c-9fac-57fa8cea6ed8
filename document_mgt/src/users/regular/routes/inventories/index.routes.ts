import { Router } from 'express';
import ProductRouter from './product.routes';
import ProductCategoryRouter from './category.routes';
import { isProductionEnv } from '../../../../utilities/guards';
import { RateLimiters } from '../../../../middlewares/utils/rate-limiter.middleware';
import container from '../../../../containers/container.global';
import { extractOrgDetailsFromRequest } from '../../../../middlewares/utils/utils.middleware';

const OrgInventoryRouter = Router({ mergeParams: true });

if (isProductionEnv) {
  OrgInventoryRouter.use(RateLimiters.organizationRequest);
}

const Auth = container.resolve('authMiddleware');

OrgInventoryRouter.use(Auth.authenticateUser.bind(Auth));
OrgInventoryRouter.use(Auth.validateActiveSubscription.bind(Auth));
OrgInventoryRouter.use(extractOrgDetailsFromRequest);

OrgInventoryRouter.use('/products', ProductRouter);
OrgInventoryRouter.use('/categories', ProductCategoryRouter);

export default OrgInventoryRouter;
