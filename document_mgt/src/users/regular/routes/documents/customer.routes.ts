import express from 'express';
import container from '../../../../containers/container.global';
import {
  validateCreateCustomers,
  validateUpdateCustomers,
} from '../../../../middlewares/validation/customer.validators';
import { USERAPP_GLOBAL_ACCESS } from '../../../../constants/values.constants';
import * as validation from '../../../../middlewares/validation/global.validators';
import { uuidParamSchema } from '../../../../middlewares/validation/helpers.utils';

const customerRoutes = express.Router();
const authController = container.resolve('authMiddleware');
const customerController = container.resolve('customerControllers');

customerRoutes.get('/search', customerController.searchCustomer.bind(customerController));

customerRoutes.get(
  '/:id',
  validation.validateParamsBody(uuidParamSchema),
  customerController.getCustomer.bind(customerController)
);

customerRoutes.get('/', customerController.getCustomers.bind(customerController));

customerRoutes.use(authController.authRestrictTo(USERAPP_GLOBAL_ACCESS));

customerRoutes.put(
  '/:id',
  validation.validateParamsBody(uuidParamSchema),
  validateUpdateCustomers,
  customerController.updateCustomer.bind(customerController)
);

customerRoutes.delete('/:id', customerController.deleteCustomer.bind(customerController));

customerRoutes.post(
  '/',
  validateCreateCustomers,
  customerController.createCustomer.bind(customerController)
);

export default customerRoutes;
