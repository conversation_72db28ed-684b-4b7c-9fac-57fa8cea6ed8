import express from 'express';
import container from '../../../../containers/container.global';
import { validateParamsBody } from '../../../../middlewares/validation/global.validators';
import { uuidParamSchema } from '../../../../middlewares/validation/helpers.utils';

const serviceRoutes = express.Router();
const serviceController = container.resolve('serviceControllers');

serviceRoutes.get('/', serviceController.getServices.bind(serviceController));

serviceRoutes.get(
  '/:id',
  validateParamsBody(uuidParamSchema),
  serviceController.getService.bind(serviceController)
);

export default serviceRoutes;
