import { Router } from 'express';
import invoiceRoutes from './invoice.routes';
import customerRoutes from './customer.routes';
import itemRoutes from './item.routes';
import serviceRoutes from './service.routes';
import statRoutes from './stats.routes';
import documentRoutes from './document.routes';
import { isProductionEnv } from '../../../../utilities/guards';
import { RateLimiters } from '../../../../middlewares/utils/rate-limiter.middleware';
import container from '../../../../containers/container.global';
import { extractOrgDetailsFromRequest } from '../../../../middlewares/utils/utils.middleware';

const OrgDocumentRouter = Router({ mergeParams: true });

if (isProductionEnv) {
  OrgDocumentRouter.use(RateLimiters.organizationRequest);
}

const Auth = container.resolve('authMiddleware');

OrgDocumentRouter.use(Auth.authenticateUser.bind(Auth));
OrgDocumentRouter.use(Auth.validateActiveSubscription.bind(Auth));
OrgDocumentRouter.use(extractOrgDetailsFromRequest);

OrgDocumentRouter.use(['/invoices'], invoiceRoutes);
OrgDocumentRouter.use(['/customers'], customerRoutes);
OrgDocumentRouter.use(['/items'], itemRoutes);
OrgDocumentRouter.use(['/services'], serviceRoutes);
OrgDocumentRouter.use(['/stats'], statRoutes);
OrgDocumentRouter.use(['/'], documentRoutes);

export default OrgDocumentRouter;
