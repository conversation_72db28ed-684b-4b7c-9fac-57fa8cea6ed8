import Document, { DocumentAttributes } from '../../../models/documents.model';
import { DocumentTypes, FilterDocument } from '../../../types/document.types';
import BaseServices from '../../../base.service';
import AdminDocumentUtils from './utils/document.utils.admin';
import { TimeFrameFilterType } from '../../../types/global.types';
import { getPeriodFilterStartAndEndDates } from '../../../utilities/global.utilities';
import { WhereOptions } from 'sequelize';

export default class AdminDocumentServices extends BaseServices<Document> {
  private async getDocumentsByType(
    orgId: string,
    type: DocumentTypes,
    offset: number,
    limit: number
  ) {
    const documents = (await this.getManyAndCount(
      { orgId, type },
      {
        include: AdminDocumentUtils.getDocumentIncludeQuery(type),
        order: [['createdAt', 'DESC']],
        offset,
        limit,
      },
      false
    )) as { count: number; rows: Document[] };

    const data = documents.rows.map((d) =>
      AdminDocumentUtils.transformDocumentWithLinkedDocuments(d.get({ plain: true }))
    );

    return { count: documents.count, rows: data };
  }

  async getDocuments(orgId: string, offset = 0, limit = 50) {
    const documents = (await this.getManyAndCount({ orgId }, { offset, limit }, true)) as {
      rows: DocumentAttributes[];
      count: number;
    };

    return documents;
  }

  async getDocumentsByFilter(orgId: string, filter: FilterDocument, offset = 0, limit = 50) {
    return this.getDocumentsByType(orgId, filter.type, offset, limit);
  }

  // get total invoice count and amount for the timeframe
  private async getOverallInvoiceTotals(whereQuery: WhereOptions<DocumentAttributes>) {
    const data = (await this.getOne(
      whereQuery,
      AdminDocumentUtils.getOverallInvoiceTotalsFindOptions(),
      true
    )) as unknown as {
      totalAmount: number;
      count: number;
    };

    return {
      totalAmount: Number(data?.totalAmount ?? 0),
      count: Number(data?.count ?? 0),
    };
  }

  // get top organization with total amount, name and invoice count per organization
  private async getTopGrossingOrganization(
    whereQuery: WhereOptions<DocumentAttributes>,
    limit: number
  ) {
    const data = (await this.getMany(
      whereQuery,
      AdminDocumentUtils.getTopGrossingOrganizationFindOptions(limit),
      true
    )) as unknown as {
      orgId: string;
      organizationName: string;
      totalAmount: number;
      count: number;
    }[];

    return data;
  }

  async getDocumentStats(timeFrameFilter: TimeFrameFilterType) {
    const { startDate, endDate } = getPeriodFilterStartAndEndDates(new Date(), timeFrameFilter);
    const invoiceWhere = AdminDocumentUtils.getInvoiceDocumentStatsWhereQuery(startDate, endDate);

    // get total invoice count and amount for the timeframe
    const overallInvoiceTotals = await this.getOverallInvoiceTotals(invoiceWhere);

    // get top 6 organization with total amount, name and invoice count per organization
    const top6GrossingOrganizations = await this.getTopGrossingOrganization(invoiceWhere, 6);

    return {
      overallInvoiceTotals,
      top6Organizations: top6GrossingOrganizations,
    };
  }
}
