import { FindOptions, Includeable, Op, Sequelize, WhereOptions } from 'sequelize';
import { DOCUMENT_STATUS, DOCUMENT_TYPE } from '../../../../constants/values.constants';
import CreditNote, { CreditNoteAttributes } from '../../../../models/credit-note.models';
import Customer from '../../../../models/customer.model';
import Document, { DocumentAttributes } from '../../../../models/documents.model';
import Invoice, { InvoiceAttributes } from '../../../../models/invoice.models';
import Items from '../../../../models/item.model';
import Receipt, { ReceiptAttributes } from '../../../../models/receipt.models';
import Services from '../../../../models/service.model';
import { DocumentTypes, LinkedDocument } from '../../../../types/document.types';
import { isProductEntity } from '../../../../utilities/guards';

export default class AdminDocumentUtils {
  private static getDataForLinkedDocument(document: DocumentAttributes) {
    return {
      type: document.type,
      documentNumber: document.documentNumber,
      status: document.status,
      createdAt: document.createdAt,
    };
  }

  private static transformLinkedReceiptsAndCreditNotes(
    receipts: ReceiptAttributes[],
    creditNotes: CreditNoteAttributes[]
  ) {
    const linkedDocuments: LinkedDocument[] = [];

    if (receipts && receipts.length > 0) {
      receipts.forEach((r) => {
        const receiptDocument = r.Document;
        linkedDocuments.push(this.getDataForLinkedDocument(receiptDocument));
      });
    }

    if (creditNotes && creditNotes.length > 0) {
      creditNotes.forEach((c) => {
        const creditNoteDocument = c.Document;
        linkedDocuments.push(this.getDataForLinkedDocument(creditNoteDocument));
      });
    }

    return linkedDocuments;
  }

  private static transformLinkedInvoice(invoice: InvoiceAttributes) {
    const linkedDocuments: LinkedDocument[] = [];

    if (invoice && Object.keys(invoice).length > 0) {
      const invoiceDocument = invoice.Document;
      linkedDocuments.push(this.getDataForLinkedDocument(invoiceDocument));
    }

    return linkedDocuments;
  }

  static transformDocumentWithLinkedDocuments(document: DocumentAttributes) {
    const {
      Customer: customer,
      items: items,
      services: services,
      Invoice: associatedInvoice,
      Receipt: associatedReceipt,
      creditNote: associatedCreditNote,
      ...docInfo
    } = document;

    const customerInfo =
      customer && Object.keys(customer).length > 0
        ? {
            customerName: customer.name,
            customerEmail: customer.email ?? '',
            customerAddress: customer.address ?? '',
            customerPhoneNumber: customer.phoneNumber ?? '',
          }
        : {};

    const entities = isProductEntity(docInfo.entityType) ? (items ?? []) : (services ?? []);

    let linkedDocuments: LinkedDocument[] = [];

    // get linked receipts and creditnotes for an invoice document
    if (document.type === DOCUMENT_TYPE.INVOICE) {
      const { Receipts: receipts, creditNotes: creditNotes } = associatedInvoice;

      linkedDocuments = this.transformLinkedReceiptsAndCreditNotes(receipts, creditNotes);
    }

    // get linked invoice for receipt
    else if (document.type === DOCUMENT_TYPE.RECEIPT) {
      const { Invoice: invoice } = associatedReceipt;
      linkedDocuments = this.transformLinkedInvoice(invoice);
    }

    // get linked invoice for creditnote
    else {
      const { Invoice: invoice } = associatedCreditNote;
      linkedDocuments = this.transformLinkedInvoice(invoice);
    }

    return {
      ...docInfo,
      ...customerInfo,
      entities,
      linkedDocuments,
    };
  }

  static getDocumentIncludeQuery(documentType: DocumentTypes) {
    const commonIncludeQuery = [
      { model: Customer },
      { model: Items },
      { model: Services },
    ] as Includeable[];

    const invoiceDocumentInclude = [
      ...commonIncludeQuery,
      {
        model: Invoice,
        include: [
          {
            model: Receipt,
            include: [{ model: Document }],
          },
          {
            model: CreditNote,
            include: [{ model: Document }],
          },
        ],
      },
    ] as Includeable[];

    const creditNoteDocumentInclude = [
      ...commonIncludeQuery,
      {
        model: CreditNote,
        include: [
          {
            model: Invoice,
            include: [{ model: Document }],
          },
        ],
      },
    ] as Includeable[];

    const receiptDocumentInclude = [
      ...commonIncludeQuery,
      {
        model: Receipt,
        include: [
          {
            model: Invoice,
            include: [{ model: Document }],
          },
        ],
      },
    ] as Includeable[];

    const includeMap: Record<DocumentTypes, Includeable[]> = {
      [DOCUMENT_TYPE.INVOICE]: invoiceDocumentInclude,
      [DOCUMENT_TYPE.CREDIT_NOTE]: creditNoteDocumentInclude,
      [DOCUMENT_TYPE.RECEIPT]: receiptDocumentInclude,
    };

    return includeMap[documentType] as Includeable[];
  }

  static getInvoiceDocumentStatsWhereQuery(startDate: Date, endDate: Date) {
    const where: WhereOptions<DocumentAttributes> = {
      type: DOCUMENT_TYPE.INVOICE,
      createdAt: {
        [Op.between]: [startDate, endDate],
      },
      status: {
        [Op.ne]: DOCUMENT_STATUS.DRAFT,
      },
    };

    return where;
  }

  static getTopGrossingOrganizationFindOptions(limit: number) {
    const options: FindOptions<DocumentAttributes> = {
      limit,
      group: ['orgId', 'businessName'],
      attributes: [
        'orgId',
        ['businessName', 'organizationName'],
        [Sequelize.fn('sum', Sequelize.col('totalAmount')), 'totalAmount'],
        [Sequelize.fn('count', Sequelize.col('id')), 'count'],
      ],
      order: [[Sequelize.fn('sum', Sequelize.col('totalAmount')), 'DESC']],
    };

    return options;
  }

  static getOverallInvoiceTotalsFindOptions() {
    const options: FindOptions<DocumentAttributes> = {
      attributes: [
        [Sequelize.fn('sum', Sequelize.col('totalAmount')), 'totalAmount'],
        [Sequelize.fn('count', Sequelize.col('id')), 'count'],
      ],
    };

    return options;
  }
}
