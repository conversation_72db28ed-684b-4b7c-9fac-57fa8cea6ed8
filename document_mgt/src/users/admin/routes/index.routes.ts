import { Router } from 'express';
import container from '../../../containers/container.global';
import { isProductionEnv } from '../../../utilities/guards';
import { RateLimiters } from '../../../middlewares/utils/rate-limiter.middleware';
import adminDocumentRouter from './document.routes';

const AdminAccessRouter = Router({ mergeParams: true });

if (isProductionEnv) {
  AdminAccessRouter.use(RateLimiters.adminRequest);
}

const Auth = container.resolve('authMiddleware');
AdminAccessRouter.use(Auth.authenticateAdminUser.bind(Auth));

AdminAccessRouter.use('/documents', adminDocumentRouter);

// AdminAccessRouter.use([`/`], documentRouter);
// AdminAccessRouter.use([`/customers`], customerRouter);
// AdminAccessRouter.use([`/invoices`], invoiceRouter);
// AdminAccessRouter.use([`/items`], itemRouter);
// AdminAccessRouter.use([`/services`], serviceRouter);
// AdminAccessRouter.use([`/stats`], statRouter);

export default AdminAccessRouter;
