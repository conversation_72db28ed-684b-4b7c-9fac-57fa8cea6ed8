import express from 'express';
import container from '../../../containers/container.global';
import { validateQueryParams } from '../../../middlewares/validation/global.validators';
import {
  filterDocumentsSchema,
  organizationIdQuerySchema,
} from '../../../middlewares/validation/schemas/query-params/document.schema';
import { extractOrgDetailsFromAdminRequest } from '../../../middlewares/utils/utils.middleware';
import { timeFrameFilterSchema } from '../../../middlewares/validation/schemas/query-params/global.schema';

const adminDocumentRouter = express.Router({ mergeParams: true });
const adminDocumentControllers = container.resolve('adminDocumentControllers');

adminDocumentRouter.get(
  '/stats',
  validateQueryParams(timeFrameFilterSchema),
  adminDocumentControllers.getDocumentStats.bind(adminDocumentControllers)
);

adminDocumentRouter.use(validateQueryParams(organizationIdQuerySchema));
adminDocumentRouter.use(extractOrgDetailsFromAdminRequest);

adminDocumentRouter.get(
  '/filter',
  validateQueryParams(filterDocumentsSchema),
  adminDocumentControllers.getDocumentsByFilter.bind(adminDocumentControllers)
);

adminDocumentRouter.get('/', adminDocumentControllers.getDocuments.bind(adminDocumentControllers));

// adminDocumentRouter.get(
//   '/:documentId',
//   adminDocumentControllers.getDocument.bind(adminDocumentControllers)
// );
// adminDocumentRouter.get(
//   '/search',
//   adminDocumentControllers.searchDocument.bind(adminDocumentControllers)
// );
// adminDocumentRouter.get(
//   '/archived-documents',
//   adminDocumentControllers.getArchiveDocuments.bind(adminDocumentControllers)
// );

// adminDocumentRouter.get(
//   '/download',
//   validateDownloadDocumentByDocumentNumber,
//   processDocumentDataByDocumentNumber,
//   documentControllers.downloadDocument.bind(documentControllers)
// );

// adminDocumentRouter.post(
//   '/send',
//   validateSendDocument,
//   documentControllers.sendPdfDocumentToEmail.bind(documentControllers)
// );

export default adminDocumentRouter;
