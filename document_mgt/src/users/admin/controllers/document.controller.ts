import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import { getResponse, ResponseMeta } from '../../../utilities/responses.utilities';
import { DOCUMENT_MESSAGES } from '../../../constants/responses.constants';
import { USER_ACTIONS } from '../../../constants/values.constants';
import { getPagination } from '../../../utilities/global.utilities';
import { getAdminAction } from './controller.helper';
import AdminDocumentServices from '../services/document.admin.service';
import { AppResponse, TimeFrameFilterType } from '../../../types/global.types';
import { FilterDocument } from '../../../types/document.types';

export default class AdminDocumentControllers extends RequestHandlerErrorWrapper {
  constructor(private adminDocumentServices: AdminDocumentServices) {
    super();
  }

  async getDocuments(req: Request, res: AppResponse) {
    const { orgId } = res.locals;
    const { offset, limit, page } = getPagination(req);

    const result = await this.adminDocumentServices.getDocuments(orgId, offset, limit);
    const { rows: data, count } = result;

    const meta: ResponseMeta = {
      count,
      limit,
      page,
      totalCount: count,
    };

    return getResponse(
      res,
      getAdminAction(USER_ACTIONS.getDocuments),
      DOCUMENT_MESSAGES.getDocuments,
      data,
      meta
    );
  }

  async getDocumentsByFilter(req: Request, res: AppResponse) {
    const { orgId } = res.locals;
    const filter = req.query as unknown as FilterDocument;
    const { offset, limit, page } = getPagination(req);

    const result = await this.adminDocumentServices.getDocumentsByFilter(
      orgId,
      filter,
      offset,
      limit
    );

    const { rows: data, count } = result;

    const meta: ResponseMeta = {
      count: data.length,
      limit,
      page,
      totalCount: count,
    };

    return getResponse(
      res,
      getAdminAction(USER_ACTIONS.filterDocument),
      DOCUMENT_MESSAGES.getDocuments,
      data,
      meta
    );
  }

  async getDocumentStats(req: Request, res: Response) {
    const timeFrameFilter = req.query.period as TimeFrameFilterType;
    const result = await this.adminDocumentServices.getDocumentStats(timeFrameFilter);

    return getResponse(res, getAdminAction(USER_ACTIONS.getStats), DOCUMENT_MESSAGES.getStats, {
      ...result,
    });
  }
}
