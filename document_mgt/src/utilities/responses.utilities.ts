import * as Sentry from '@sentry/node';
import { Response } from 'express';
// import UtilityAPIs from '../api/utilities';
import { LogDetails, RequestLogDetails } from '../interfaces/logs.interface';
import { getErrorCode } from '../helpers/error.helpers';
import { isValuePresent } from './guards';
import container from '../containers/container.global';

export interface ResponseMeta {
  count?: number;
  page?: number;
  limit?: number;
  totalCount?: number;
  totalPages?: number;
  [key: string]: any;
}

function processLogDetails(
  requestLogDetails: RequestLogDetails,
  action: string,
  statusCode: number,
  message: string,
  data?: any
): LogDetails {
  return {
    anonymous: requestLogDetails.userDetails?.userId ? false : true,
    userId: requestLogDetails?.userDetails?.userId,
    orgId: requestLogDetails?.userDetails?.orgId ?? '',
    action,
    details: {
      ...requestLogDetails,
      responseDetails: {
        statusCode,
        message,
        data,
      },
    },
  };
}

async function sendLog(
  res: Response,
  action: string,
  statusCode: number,
  message: string,
  data?: any
) {
  const requestLogDetails = res.locals.requestLogDetails as RequestLogDetails;
  const logDetails: LogDetails = processLogDetails(
    requestLogDetails,
    action,
    statusCode,
    message,
    data
  );

  await container.resolve('backgroundTaskManagers').queueTasks(logDetails, 'saveRequestLogsQueue');
}

function sendSuccessResponse(
  res: Response,
  statusCode: number,
  action: string,
  message?: string,
  data?: any[] | Record<string, any>,
  meta?: ResponseMeta
) {
  process.nextTick(() => {
    sendLog(res, action, statusCode, message, data).catch((error) => {
      Sentry.captureException(error);
    });
  });

  return res
    .status(statusCode)
    .json({
      message,
      code: `S${statusCode}`,
      data,
      meta,
    })
    .end();
}

export const postResponse = async (
  res: Response,
  action: string,
  message: string,
  data?: Record<string, any>
) => {
  if (data) delete data.password;
  return sendSuccessResponse(res, 201, action, message, data);
};

export const getResponse = async (
  res: Response,
  action: string,
  message: string,
  data?: Record<string, any> | any[],
  meta?: ResponseMeta
) => {
  if (meta && isValuePresent(meta?.totalCount) && isValuePresent(meta?.limit)) {
    meta.totalPages = Math.ceil(meta.totalCount / meta.limit);
  }

  if (meta?.limit) delete meta.limit;
  if (meta?.totalCount) delete meta.totalCount;

  meta = meta
    ? {
        page: meta?.page,
        count: meta?.count,
        totalPages: meta?.totalPages,
        ...meta,
      }
    : undefined;

  return sendSuccessResponse(res, 200, action, message, data, meta);
};

export const deleteResponse = async (res: Response, action: string) => {
  return sendSuccessResponse(res, 204, action);
};

export function sendErrorWithData(res: Response, statusCode: number, message?: string, data?: any) {
  return res
    .status(statusCode)
    .json({ message, code: getErrorCode(statusCode), details: data })
    .end();
}
