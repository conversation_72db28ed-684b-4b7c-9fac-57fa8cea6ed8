// import { Response } from 'express';
// import { AppError } from '../middlewares/error_handlers/app-error';
// import { getErrorCode } from '../helpers/error.helpers';
// import { Errors } from '../constants/errors.constants';
// import { StatusCodes } from 'http-status-codes';

// export class ErrorHandler {
//   public static prodErrorHandler(err: any, res: Response) {
//     return res.status(err.statusCode).json({
//       status: 'error',
//       error: err.message,
//       code: getErrorCode(err.statusCode) || '',
//     });
//   }

//   public static devErrorHandler(err: any, res: Response) {
//     return res.status(err.statusCode).json({
//       status: err.status,
//       message: err.message,
//       code: getErrorCode(err.statusCode) || '',
//     });
//   }

//   public static handleValidationError(err: any) {
//     const errors = err.errors.map((err: any) => ({
//       message: err.message.replace(/['"\\]/g, ''),
//     }));
//     return new AppError(errors, 400);
//   }

//   public static handleAxiosError(err: any) {
//     const error = err.message;
//     return new AppError(error, err.status);
//   }

//   public static handleDatabaseError(err: any) {
//     let errorMsg: string;

//     const pattern = /invalid input value for enum enum_users_role/;

//     if (pattern.test(err.message)) errorMsg = 'Invalid role input';
//     else errorMsg = err.message;
//     return new AppError(errorMsg, 400);
//   }

//   public static async handleForeignKeyConstraintError(err: any) {
//     const error: string = err.message;
//     return new AppError(error, 400);
//   }

//   public static handleInternalServerError() {
//     return new AppError(Errors.SERVER_ERROR, StatusCodes.INTERNAL_SERVER_ERROR);
//   }

//   public static async handleFoError(err: any) {
//     const error: string = err.message;
//     return new AppError(error, 400);
//   }

//   public static async handleTokenExpiredError() {
//     return new AppError('Token Expired', 401);
//   }

//   public static async RateLimitExceeded(err: any, res: Response) {
//     const remainingAttempt = err.retryAfter;
//     return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
//       message: Errors.RATE_LIMIT_EXCEEDED,
//       remainingAttempt,
//     });
//   }
// }
