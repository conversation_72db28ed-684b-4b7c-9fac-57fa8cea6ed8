import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from './helpers/class.helpers';
import { NotFoundError } from './middlewares/error_handlers/app-error';
import { formatDateToYearMonthDayTime, getUserTimeZone } from './utilities/global.utilities';
import RequestIP from 'request-ip';

export default class UtilitiesControllers extends RequestHandlerErrorWrapper {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async resourceNotFound(req: Request, _res: Response) {
    throw new NotFoundError(
      `${req.method} not allowed for ${req.originalUrl} OR, can't find requested resource on the server`
    );
  }

  async getDocumentation(_req: Request, res: Response) {
    const url = process.env.API_DOCS || undefined;

    if (!url) {
      throw new NotFoundError('API documentation not published');
    }

    res.redirect(url);
  }

  async getHealth(req: Request, res: Response) {
    const time = formatDateToYearMonthDayTime(new Date(), getUserTimeZone(req));
    return res.status(200).json({
      status: 'success',
      message: 'documents application running successfully',
      data: {
        time,
        ipAddress: RequestIP.getClientIp(req),
        timezone: getUserTimeZone(req),
        device: req.headers['user-agent'],
      },
    });
  }
}
