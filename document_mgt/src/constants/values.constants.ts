import { ROLES } from '../models/enums';
import { DocumentTypes } from '../types/document.types';
import { isDevelopmentEnv, isProductionEnv, isTestENV } from '../utilities/guards';

export const HUNDRED = 100;
export const BASE_10 = 10;
export const ZERO = 0;
export const OTP_EXPIRE_TIME: number = 15;

export const DOCUMENT_TYPE = {
  INVOICE: 'invoice',
  RECEIPT: 'receipt',
  CREDIT_NOTE: 'creditnote',
} as const;

export const DOCUMENT_TYPE_ARRAY = Object.values(DOCUMENT_TYPE);

export const DOCUMENT_STATUS = {
  DRAFT: 'draft',
  AWAITING_PAYMENT: 'awaiting payment',
  PARTIAL_PAYMENT: 'partial payment',
  COMPLETED: 'completed',
} as const;

export const DOCUMENT_STATUS_ARRAY = Object.values(DOCUMENT_STATUS);

export const ALLOWED_ARCHIVES = [
  DOCUMENT_STATUS.DRAFT,
  DOCUMENT_STATUS.AWAITING_PAYMENT,
  DOCUMENT_STATUS.PARTIAL_PAYMENT,
];

export const ZERO_DOCUMENT_NUMBER: Record<DocumentTypes, string> = {
  invoice: 'INV-000',
  receipt: 'REC-000',
  creditnote: 'CRN-000',
} as const;

export const DOCUMENT_ENTITY_TYPE = {
  PRODUCT: 'product',
  SERVICE: 'service',
};

export const ROLE = {
  SUPERADMIN: 'SUPERADMIN',
  ADMIN: 'ADMIN',
  CUSTOMER_SUPPORT: 'SUPPORT',
  OWNER: 'OWNER',
  MANAGER: 'MANAGER',
  BASIC: 'BASIC',
};

export const ROLES_ARRAY = Object.values(ROLE);
export const DOCUMENT_ENTITY_TYPE_ARRAY = Object.values(DOCUMENT_ENTITY_TYPE);

export const FILE_EXTENSION = {
  PDF: 'pdf',
  JPEG: 'jpeg',
  JPG: 'jpg',
  PNG: 'png',
};

export const NOTIFICATION_ACTION = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
};

export const DGT_AUTH_TYPE = {
  KEY: 'dgtauth',
};
export const DGT_AUTH_TYPE_ARRAY = Object.values(DGT_AUTH_TYPE);

const BASE_ORIGINS = ['https://digit-tally.io'];

const DEV_ORIGINS = [
  'http://localhost:9628',
  'http://localhost:8752',
  'http://localhost:3932',
  'http://localhost:8756',
  'http://localhost:8759',
  'http://localhost:7859',
  'http://localhost:8795',
  'http://localhost:8579',
  'http://localhost:9758',
  'http://localhost:8957',
  'http://127.0.0.1:7859',
  'http://127.0.0.1:8795',
  'http://127.0.0.1:8579',
  'http://127.0.0.1:9758',
  'http://127.0.0.1:8957',
  'http://127.0.0.1:9628',
  'http://127.0.0.1:8752',
  'http://127.0.0.1:3932',
  'http://127.0.0.1:8756',
  'http://127.0.0.1:8759',
  'http://0.0.0.0',
  'http://***********:8080',
  'http://*************:3000',
  'http://*************:3000',
  'http://*************:3000',
];

// export const PAYMENT_BASE_URL = !isProductionEnv
//   ? 'https://stgsvr.digit-tally.io/payments'
//   : 'https://app.digit.tally.io/payments';
export const PAYSLIP_BASE_URL = !isProductionEnv
  ? 'https://stgsvr008.digit-tally.io'
  : 'https://digit-tally.io';

export const APP_ORIGINS = {
  DEV_USER_APP: process.env.DEV_USER_APP,
  DEV_ADMIN: process.env.DEV_ADMIN_APP,
  PROD_USER_APP: process.env.PROD_USER_APP,
  PROD_ADMIN: process.env.PROD_ADMIN_APP,
};

export const ALLOWED_ORIGINS =
  isDevelopmentEnv || isTestENV
    ? [...DEV_ORIGINS, ...BASE_ORIGINS, process.env.DEV_USER_APP, process.env.DEV_ADMIN_APP]
    : [...BASE_ORIGINS, process.env.PROD_USER_APP, process.env.PROD_ADMIN_APP];

export const ADMIN_ORIGINS = [process.env.PROD_ADMIN_APP, process.env.DEV_ADMIN_APP];

export const USER_APP_ORIGINS =
  isDevelopmentEnv || isTestENV
    ? [...DEV_ORIGINS, APP_ORIGINS.DEV_USER_APP, APP_ORIGINS.PROD_USER_APP]
    : [APP_ORIGINS.DEV_USER_APP, APP_ORIGINS.PROD_USER_APP];

export const ADMIN_APP_ORIGINS =
  isDevelopmentEnv || isTestENV
    ? [...DEV_ORIGINS, APP_ORIGINS.DEV_ADMIN, APP_ORIGINS.PROD_ADMIN]
    : [APP_ORIGINS.DEV_ADMIN, APP_ORIGINS.PROD_ADMIN];

export const USERAPP_GLOBAL_ACCESS = [ROLES.SUPERADMIN, ROLES.OWNER, ROLES.MANAGER];
export const USERAPP_ACCESS = [ROLES.SUPERADMIN, ROLES.OWNER, ROLES.MANAGER, ROLES.BASIC];
export const ADMINAPP_GLOBAL_ACCESS = [ROLES.SUPERADMIN, ROLES.ADMIN];
export const SUPER_ADMIN_ACCESS = [ROLES.SUPERADMIN.toLowerCase()];
export const OWNER_ACCESS = [ROLES.SUPERADMIN, ROLES.OWNER];
export const ADMIN_ACCESS = [ROLES.SUPERADMIN, ROLES.ADMIN, ROLES.CUSTOMER_SUPPORT];
export const MUTABLE_ADMIN_ROLES = [ROLES.ADMIN, ROLES.CUSTOMER_SUPPORT];

export const HTTP_METHODS = {
  POST: 'POST',
  GET: 'GET',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const USER_ACTIONS = {
  // stats
  getStats: 'GET_STATS',

  // services & Items
  getServices: 'GET_SERVICES',
  getService: 'GET_SERVICE',
  getItems: 'GET_ITEMS',
  getItem: 'GET_ITEM',

  // customers
  createCustomer: 'CREATE_CUSTOMER',
  getCustomers: 'GET_CUSTOMERS',
  getCustomer: 'GET_CUSTOMER',
  editCustomer: 'EDIT_CUSTOMER',
  deleteCustomer: 'DELETE_CUSTOMER',
  searchCustomer: 'SEARCH_CUSTOMER',

  // invoices
  getInvoices: 'GET_INVOICES',
  searchInvoices: 'SEARCH_INVOICES',

  // documents
  createInvoice: 'CREATE_INVOICE',
  createReceipt: 'CREATE_RECEIPT',
  createCreditNote: 'CREATE_CREDIT_NOTE',
  updateDocument: 'UPDATE_DOCUMENT',
  getDocuments: 'GET_DOCUMENTS',
  getAllDocuments: 'GET_DOCUMENTS',
  getRecentDocuments: 'GET_RECENT_DOCUMENTS',
  filterDocument: 'FILTER_DOCUMENT',
  getDocument: 'GET_DOCUMENT',
  getDocumentByDocumentId: 'GET_DOCUMENT',
  getLastDocumentNumber: 'LAST_DOCUMENT_NUMBER',
  archiveDocument: 'ARCHIVE_DOCUMENT',
  unArchiveDocument: 'UNARCHIVE_DOCUMENT',
  getArchiveDocuments: 'GET_ARCHIVED_DOCUMENTS',
  deleteDocument: 'DELETE_DOCUMENT',
  downloadDocument: 'DOWNLOAD_DOCUMENT',
  sendPdfDocumentToEmail: 'SEND_PDF_DOCUMENT',
  _sendPdfDocumentToEmail: 'SEND_PDF_DOCUMENT',
  sendReminder: 'SEND_REMINDER',
  searchDocument: 'SEARCH_DOCUMENT',

  // system-Generated Documents
  processSystemInvoiceDocument: 'PROCESS_SYSTEM_INVOICE',
  processSystemReceiptDocument: 'PROCESS_SYSTEM_RECEIPT',
  processSystemPaymentDocument: 'PROCESS_SYSTEM_PAYMENT',
};

export const DEFINED_MS_ERROR_CODES_WITH_MESSAGES = {
  400: 'EB400',
  401: 'EA401',
  403: 'EP403',
  404: 'EN404',
  409: 'EC409',
  500: 'ES500',
} as const;

export const DEFINED_MS_ERROR_CODES_ARRAY = Object.values(DEFINED_MS_ERROR_CODES_WITH_MESSAGES);

export const API_VERSION = '/api/v1';

export const INVENTORY_STATUS = {
  active: 'active',
  archived: 'archived',
  outOfStock: 'out of stock',
};

export const INVENTORY_STATUS_ARRAY = Object.values(INVENTORY_STATUS);

export const COUNTRY_NAMES_AND_TWO_LETTER_CODES = {
  afghanistan: 'AF',
  albania: 'AL',
  algeria: 'DZ',
  'american samoa': 'AS',
  andorra: 'AD',
  angola: 'AO',
  anguilla: 'AI',
  antarctica: 'AQ',
  'antigua and barbuda': 'AG',
  argentina: 'AR',
  armenia: 'AM',
  aruba: 'AW',
  australia: 'AU',
  austria: 'AT',
  azerbaijan: 'AZ',
  bahamas: 'BS',
  bahrain: 'BH',
  bangladesh: 'BD',
  barbados: 'BB',
  belarus: 'BY',
  belgium: 'BE',
  belize: 'BZ',
  benin: 'BJ',
  bermuda: 'BM',
  bhutan: 'BT',
  bolivia: 'BO',
  'bosnia and herzegovina': 'BA',
  botswana: 'BW',
  brazil: 'BR',
  brunei: 'BN',
  bulgaria: 'BG',
  'burkina faso': 'BF',
  burundi: 'BI',
  cambodia: 'KH',
  cameroon: 'CM',
  canada: 'CA',
  'cape verde': 'CV',
  'central african republic': 'CF',
  chad: 'TD',
  chile: 'CL',
  china: 'CN',
  colombia: 'CO',
  comoros: 'KM',
  'congo (brazzaville)': 'CG',
  'congo (kinshasa)': 'CD',
  'costa rica': 'CR',
  croatia: 'HR',
  cuba: 'CU',
  cyprus: 'CY',
  'czech republic': 'CZ',
  denmark: 'DK',
  djibouti: 'DJ',
  dominica: 'DM',
  'dominican republic': 'DO',
  ecuador: 'EC',
  egypt: 'EG',
  'el salvador': 'SV',
  'equatorial guinea': 'GQ',
  eritrea: 'ER',
  estonia: 'EE',
  eswatini: 'SZ',
  ethiopia: 'ET',
  fiji: 'FJ',
  finland: 'FI',
  france: 'FR',
  gabon: 'GA',
  gambia: 'GM',
  georgia: 'GE',
  germany: 'DE',
  ghana: 'GH',
  greece: 'GR',
  greenland: 'GL',
  grenada: 'GD',
  guatemala: 'GT',
  guinea: 'GN',
  'guinea-bissau': 'GW',
  guyana: 'GY',
  haiti: 'HT',
  honduras: 'HN',
  'hong kong': 'HK',
  hungary: 'HU',
  iceland: 'IS',
  india: 'IN',
  indonesia: 'ID',
  iran: 'IR',
  iraq: 'IQ',
  ireland: 'IE',
  israel: 'IL',
  italy: 'IT',
  jamaica: 'JM',
  japan: 'JP',
  jordan: 'JO',
  kazakhstan: 'KZ',
  kenya: 'KE',
  kiribati: 'KI',
  kuwait: 'KW',
  kyrgyzstan: 'KG',
  laos: 'LA',
  latvia: 'LV',
  lebanon: 'LB',
  lesotho: 'LS',
  liberia: 'LR',
  libya: 'LY',
  liechtenstein: 'LI',
  lithuania: 'LT',
  luxembourg: 'LU',
  madagascar: 'MG',
  malawi: 'MW',
  malaysia: 'MY',
  maldives: 'MV',
  mali: 'ML',
  malta: 'MT',
  mauritania: 'MR',
  mauritius: 'MU',
  mexico: 'MX',
  moldova: 'MD',
  monaco: 'MC',
  mongolia: 'MN',
  montenegro: 'ME',
  morocco: 'MA',
  mozambique: 'MZ',
  myanmar: 'MM',
  namibia: 'NA',
  nepal: 'NP',
  netherlands: 'NL',
  'new zealand': 'NZ',
  nicaragua: 'NI',
  niger: 'NE',
  nigeria: 'NG',
  'north korea': 'KP',
  'north macedonia': 'MK',
  norway: 'NO',
  oman: 'OM',
  pakistan: 'PK',
  palestine: 'PS',
  panama: 'PA',
  'papua new guinea': 'PG',
  paraguay: 'PY',
  peru: 'PE',
  philippines: 'PH',
  poland: 'PL',
  portugal: 'PT',
  qatar: 'QA',
  romania: 'RO',
  russia: 'RU',
  rwanda: 'RW',
  'saudi arabia': 'SA',
  senegal: 'SN',
  serbia: 'RS',
  seychelles: 'SC',
  'sierra leone': 'SL',
  singapore: 'SG',
  slovakia: 'SK',
  slovenia: 'SI',
  'solomon islands': 'SB',
  somalia: 'SO',
  'south africa': 'ZA',
  'south korea': 'KR',
  'south sudan': 'SS',
  spain: 'ES',
  'sri lanka': 'LK',
  sudan: 'SD',
  suriname: 'SR',
  sweden: 'SE',
  switzerland: 'CH',
  syria: 'SY',
  taiwan: 'TW',
  tajikistan: 'TJ',
  tanzania: 'TZ',
  thailand: 'TH',
  togo: 'TG',
  tonga: 'TO',
  'trinidad and tobago': 'TT',
  tunisia: 'TN',
  turkey: 'TR',
  turkmenistan: 'TM',
  uganda: 'UG',
  ukraine: 'UA',
  'united arab emirates': 'AE',
  'united kingdom': 'GB',
  'united states': 'US',
  uruguay: 'UY',
  uzbekistan: 'UZ',
  vanuatu: 'VU',
  venezuela: 'VE',
  vietnam: 'VN',
  yemen: 'YE',
  zambia: 'ZM',
  zimbabwe: 'ZW',
} as const;

export const COUNTRY_NAMES = Object.keys(COUNTRY_NAMES_AND_TWO_LETTER_CODES);

export const NOTIFICATION_EVENT_NAMES = {
  app: 'app.notifications',
  admin: 'admin.notifications',
} as const;
