export type EmailTemplatesType = {
  accountActivation: string;
  accountChange: string;
  accountDeactivation: string;
  accountReactivation: string;
  creditNoteIssuance: string;
  emailVerification: string;
  feedback: string;
  invoiceCreation: string;
  invoicePayment: string;
  invoiceSent: string;
  receiptSent: string;
  creditNoteSent: string;
  loginAlert: string;
  onboardMail: string;
  passwordChange: string;
  passwordReset: string;
  paymentReceived: string;
  privacyPolicy: string;
  profileUpdate: string;
  receiptIssuance: string;
  recurringInvoice: string;
  resetPassword: string;
  twofactorAuth: string;
  waitlist: string;
  welcome: string;
  adminRegister: string;
};

export type EmailSubjectType = {
  accountActivationMail: string;
  verifyMail: string;
  welcomeMail: string;
  adminRegisterMail: string;
  newDeviceMail: string;
  resetPasswordMail: string;
  profileUpdateMail: string;
  businessDetailsUpdateMail: string;
  passwordChangedMail: string;
  accountDeactivatedMail: string;
  accountReactivationMail: string;
  waitlistMail: string;
  invoiceReminder: string;
};
