export type ItemsDTOType = {
  id?: number;
  name: string;
  description: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  vat: number;
  amount?: number;
};

export type ServicesDTOType = {
  id?: number;
  name: string;
  description: string;
  hours: number;
  hourlyRate: number;
  discount: number;
  vat: number;
  amount?: number;
};

export type ItemMessageType = {
  created: string;
  getItems: string;
  getItem: string;
  updateItems: string;
  deleteItems: string;
};

export type ServiceMessageType = {
  created: string;
  getServices: string;
  getService: string;
  updateServices: string;
  deleteServices: string;
};
