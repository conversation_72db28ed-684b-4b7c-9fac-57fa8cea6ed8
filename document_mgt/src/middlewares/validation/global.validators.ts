import { NextFunction } from '@sentry/node/build/types/integrations/tracing/nest/types';
import { Request, Response } from 'express';
import { Schema } from 'joi';
import { catchAsync } from '../../utilities/catch-async-error';
import { BadRequestError } from '../error_handlers/app-error';
import { getJoiValidationErrorMessage } from '../../helpers/error.helpers';

export function validateRequestBody(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: true,
      stripUnknown: true,
    });

    if (error) throw new BadRequestError(getJoiValidationErrorMessage(error));

    req.body = value;
    next();
  });
}

export function validateParamsBody(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.params, { abortEarly: false, stripUnknown: true });

    if (error) throw new BadRequestError(getJoiValidationErrorMessage(error));

    next();
  });
}

export function validateQueryParams(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.query, { abortEarly: true, stripUnknown: true });

    if (error) throw new BadRequestError(getJoiValidationErrorMessage(error));

    next();
  });
}
