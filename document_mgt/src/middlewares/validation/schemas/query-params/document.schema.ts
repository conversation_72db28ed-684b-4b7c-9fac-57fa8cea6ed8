import Joi from 'joi';
import { DOCUMENT_TYPE_ARRAY } from '../../../../constants/values.constants';
import { FilterDocument } from '../../../../types/document.types';

export const filterDocumentsSchema = Joi.object<FilterDocument>({
  type: Joi.string()
    .trim()
    .lowercase()
    .valid(...DOCUMENT_TYPE_ARRAY)
    .required()
    .messages({
      'any.required': 'document type is required',
      'string.base': 'document type must be a string',
      'string.empty': 'document type cannot be empty',
      'any.only': `document type must be one of the following: ${DOCUMENT_TYPE_ARRAY.join(', ')}`,
    }),
});

export const organizationIdQuerySchema = Joi.object({
  organizationId: Joi.string()
    .regex(/^(dgt-)[0-9a-zA-Z]+$/)
    .required()
    .messages({
      'string.base': 'organizationId must be a string',
      'string.empty': 'organizationId must not be empty',
      'any.required': 'organizationId is required as a query parameter',
      'string.pattern.base': 'organizationId must match pattern: dgt-xxx',
    }),
});
