import Joi from 'joi';
import { TimeFrameFilterType } from '../../../../types/global.types';
import { TIME_FRAME_FILTER_ARRAY } from '../../../../models/enums';

export const timeFrameFilterSchema = Joi.object<{ period: TimeFrameFilterType }>({
  period: Joi.string()
    .valid(...TIME_FRAME_FILTER_ARRAY)
    .required()
    .messages({
      'any.required': 'period is required',
      'string.base': 'period must be a string',
      'any.only': `period must be one of: ${TIME_FRAME_FILTER_ARRAY.join(', ')}`,
    }),
});
