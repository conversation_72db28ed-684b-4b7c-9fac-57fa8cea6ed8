import { StatusCodes } from 'http-status-codes';
import { Errors } from '../../constants/errors.constants';

export class AppError extends Error {
  public message: string;
  public readonly location?: string;
  public readonly status: string;
  public name: string;
  public readonly statusCode: number;

  public isOperational = true;

  constructor(message: string, statusCode: number, location?: string, name: string = 'AppError') {
    super(message);
    this.message = message;
    this.statusCode = statusCode;
    this.location = location;
    this.name = name;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';

    Error.captureStackTrace(this, this.constructor);
  }
}

export class BadRequestError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.BAD_REQUEST, location, 'BadRequestError');
  }
}

export class NotFoundError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.NOT_FOUND, location, 'NotFoundError');
  }
}

export class NotAuthenticatedError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.UNAUTHORIZED, location, 'UnauthorizedError');
  }
}

export class NotPermittedError extends AppError {
  constructor(message = Errors.NOT_PERMITTED, location?: string) {
    super(message, StatusCodes.FORBIDDEN, location, 'NoPermissionError');
  }
}

export class ConflictError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.CONFLICT, location, 'ConflictError');
  }
}

export class RateLimitError extends AppError {
  constructor(message = Errors.RATE_LIMIT_EXCEEDED, location?: string) {
    super(message, StatusCodes.TOO_MANY_REQUESTS, location, 'RateLimitError');
  }
}

export class InternalServerError extends AppError {
  constructor(message = Errors.SERVER_ERROR, location?: string) {
    super(message, StatusCodes.INTERNAL_SERVER_ERROR, location, 'InternalServerError');
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(message = Errors.SERVICE_UNAVAILABLE, location?: string) {
    super(message, StatusCodes.SERVICE_UNAVAILABLE, location, 'ServiceUnavailableError');
  }
}

export class GatewayError extends AppError {
  constructor(message = Errors.GATEWAY_ERROR, location: string = 'axios error') {
    super(message, StatusCodes.BAD_GATEWAY, location, 'GatewayError');
  }
}

export const handleValidationError = (err: any) => {
  const errors = err.errors.map((err: any) => ({
    message: err.message.replace(/['"\\]/g, ''),
  }));
  return new BadRequestError(errors);
};

export const handleDatabaseError = (err: any) => {
  let error: string;

  const pattern = /invalid input value for enum enum_users_role/;

  if (pattern.test(err.message)) error = 'Invalid role input';
  else error = err.message;
  return new BadRequestError(error);
};

export const handleForeignKeyConstraintError = (err: any) => {
  const error: string = err.message;
  return new BadRequestError(error);
};

export const handleUniqueConstraintError = (err: any) => {
  const error: string = err.errors[0].path + ' ' + 'exists in the database';
  return new BadRequestError(error);
};
