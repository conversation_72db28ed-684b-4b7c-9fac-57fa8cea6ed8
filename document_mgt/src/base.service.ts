import {
  Model,
  FindOptions,
  WhereOptions,
  Attributes,
  ModelStatic,
  BulkCreateOptions,
  CreateOptions,
  SaveOptions,
} from 'sequelize';
import { <PERSON>rrorWrapper } from './helpers/class.helpers';
import { NotFoundError } from './middlewares/error_handlers/app-error';

export default class BaseServices<T extends Model> extends ErrorWrapper {
  constructor(
    protected model: ModelStatic<T>,
    protected cacheServiceName?: string
  ) {
    super();
  }

  protected async getOne(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {},
    plain: boolean = true
  ): Promise<Attributes<T> | T> {
    const data = await this.model.findOne({ where, ...options });

    if (!data) throw new NotFoundError(`${String(this.model.name).toLowerCase()} not found`);

    return plain ? data.get({ plain: true }) : data;
  }

  protected async getOneOrNull(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {},
    plain: boolean = true
  ): Promise<Attributes<T> | T> | null {
    const data = await this.model.findOne({ where, ...options });

    if (data) return plain ? data.get({ plain: true }) : data;
    else return null;
  }

  protected async getMany(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {},
    plain: boolean = true
  ): Promise<(Attributes<T> | T)[]> {
    const result = await this.model.findAll({ where, ...options });

    return plain ? result.map((r) => r.get({ plain: true })) : result;
  }

  protected async getManyAndCount(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {},
    plain: boolean = true
  ): Promise<{ rows: (Attributes<T> | T)[]; count: number }> {
    const result = await this.model.findAndCountAll({ where, ...options });

    return {
      rows: plain ? result.rows.map((r) => r.get({ plain: true })) : result.rows,
      count: result.count,
    };
  }

  protected async create(
    data: Partial<Attributes<T>>,
    options: CreateOptions = {},
    plain: boolean = true
  ): Promise<Attributes<T> | T> {
    const instance = await this.model.create(data as any, options);

    return plain ? instance.get({ plain: true }) : instance;
  }

  protected async bulkCreate(
    data: Partial<Attributes<T>>[],
    options: BulkCreateOptions = {},
    plain: boolean = true
  ): Promise<(Attributes<T> | T)[]> {
    const created = await this.model.bulkCreate(data as any, { ...options });

    return plain ? created.map((i) => i.get({ plain: true })) : created;
  }

  protected async saveInstance(
    instance: T,
    options: SaveOptions = {},
    plain: boolean = true
  ): Promise<Attributes<T> | T> {
    const savedInstance = await instance.save({ returning: true, ...options });

    return plain ? savedInstance.get({ plain }) : savedInstance;
  }

  protected async count(where?: WhereOptions<Attributes<T>>) {
    return this.model.count({ where });
  }
}
