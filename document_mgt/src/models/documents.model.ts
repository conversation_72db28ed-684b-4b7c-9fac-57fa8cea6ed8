import { DataTypes, Model, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import {
  DOCUMENT_STATUS_ARRAY,
  DOCUMENT_TYPE,
  DOCUMENT_TYPE_ARRAY,
} from '../constants/values.constants';
// import { truncateToTwoDecimals } from '../utilities/global.utilities';
import { Models } from './associations.models';
import { CustomerAttributes } from './customer.model';
import { ItemAttributes } from './item.model';
import { ServiceAttributes } from './service.model';
import { InvoiceAttributes } from './invoice.models';
import { ReceiptAttributes } from './receipt.models';
import { CreditNoteAttributes } from './credit-note.models';
import { DocumentStatusTypes, DocumentTypes } from '../types/document.types';

export interface DocumentAttributes {
  id?: string;
  orgId?: string;
  customerId?: string;
  docId?: string;
  type?: DocumentTypes;
  entityType?: string;
  businessName?: string;
  businessAddress?: string;
  businessCountry?: string;
  totalDiscount?: number;
  notes?: string;
  totalVat?: number;
  amount?: number;
  totalAmount?: number;
  remainingPayableAmount?: number;
  creditNoteAmount?: number;
  creditNoteIssuableAmount?: number;
  refundableAmount?: number;
  amountPaid?: number;
  companyRegistrationNumber?: string;
  vatNumber?: string;
  taxNumber?: string;
  subTotalAmount?: number;
  creditNoteNumber?: string;
  receiptNumber?: string;
  invoiceNumber?: string;
  documentNumber?: string;
  invoiceId?: string;
  refunded?: boolean;
  archive?: boolean;
  logo?: string;
  logoThumbnail?: string;
  status?: DocumentStatusTypes;
  dateIssued?: Date | null;
  dueDate?: Date | null;

  Customer?: CustomerAttributes;
  Invoice?: InvoiceAttributes;
  Receipt?: ReceiptAttributes;
  creditNote?: CreditNoteAttributes;
  items?: ItemAttributes[];
  services?: ServiceAttributes[];

  createdAt?: Date;
  updatedAt?: Date;
}

class Document extends Model<DocumentAttributes> implements DocumentAttributes {
  public id!: string;
  public orgId!: string;
  public customerId!: string;
  public docId!: string;
  public logo!: string;
  public logoThumbnail!: string;
  public type!: DocumentTypes;
  public entityType!: string;
  public businessName!: string;
  public businessAddress!: string;
  public businessCountry!: string;
  public documentNumber!: string;
  public totalDiscount!: number;
  public notes!: string;
  public totalVat!: number;
  public totalAmount!: number;
  public remainingPayableAmount!: number;
  public refundableAmount!: number;
  public creditNoteAmount!: number;
  public amountPaid!: number;
  public subTotalAmount!: number;
  public amount!: number;
  public refunded!: boolean;
  public archive!: boolean;
  public companyRegistrationNumber!: string;
  public vatNumber!: string;
  public taxNumber!: string;
  public status!: DocumentStatusTypes;
  public dueDate!: Date | null;
  public dateIssued!: Date | null;

  Customer?: CustomerAttributes;
  Invoice?: InvoiceAttributes;
  Receipt?: ReceiptAttributes;
  creditNote?: CreditNoteAttributes;
  items?: ItemAttributes[];
  services?: ServiceAttributes[];

  public createdAt: Date;
  public updatedAt: Date;

  static associate(models: Models) {
    Document.hasOne(models.Invoice, {
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.hasOne(models.Receipt, {
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.hasOne(models.CreditNote, {
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.belongsTo(models.Customer, {
      foreignKey: 'customerId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.hasMany(models.Item, {
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.hasMany(models.Service, {
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Document.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    customerId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'customers', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    docId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    logo: DataTypes.STRING,
    logoThumbnail: DataTypes.STRING,
    type: { type: DataTypes.ENUM(...DOCUMENT_TYPE_ARRAY), allowNull: false },
    entityType: {
      type: DataTypes.ENUM('product', 'service'),
      allowNull: false,
    },
    businessName: DataTypes.STRING,
    businessAddress: DataTypes.STRING,
    businessCountry: DataTypes.STRING,
    documentNumber: { type: DataTypes.STRING, defaultValue: '' },
    notes: DataTypes.STRING,
    companyRegistrationNumber: DataTypes.STRING,
    vatNumber: DataTypes.STRING,
    taxNumber: DataTypes.STRING,
    status: {
      type: DataTypes.ENUM(...DOCUMENT_STATUS_ARRAY),
      allowNull: false,
      defaultValue: 'draft',
    },
    totalDiscount: DataTypes.FLOAT,
    totalVat: DataTypes.FLOAT,
    totalAmount: DataTypes.FLOAT,
    remainingPayableAmount: DataTypes.FLOAT,
    refundableAmount: DataTypes.FLOAT,
    creditNoteAmount: DataTypes.FLOAT,
    subTotalAmount: DataTypes.FLOAT,
    amountPaid: DataTypes.FLOAT,
    refunded: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    archive: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    dateIssued: { type: DataTypes.DATE, allowNull: true },
    dueDate: { type: DataTypes.DATE, allowNull: true },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    indexes: [{ fields: ['type'] }],
    modelName: 'Document',
    tableName: 'documents',
  }
);

Document.addHook('afterFind', async (document: Document) => {
  if (document && document.type === DOCUMENT_TYPE.INVOICE) {
    const creditNoteAmount = document.creditNoteAmount;
    const totalAmount = document.totalAmount;

    document['creditNoteIssuableAmount'] = totalAmount - creditNoteAmount;
  }
});
export default Document;
