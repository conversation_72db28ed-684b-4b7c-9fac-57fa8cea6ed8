import { DataTypes, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import Document, { DocumentAttributes } from './documents.model';
import { Models } from './associations.models';
import { ReceiptAttributes } from './receipt.models';
import { CreditNoteAttributes } from './credit-note.models';

export interface InvoiceAttributes {
  id: string;
  invoiceNumber: string;
  orgId: string;
  docId: string;
  dateIssued: Date | null;
  dueDate: Date | null;

  Document?: DocumentAttributes;
  Receipts?: ReceiptAttributes[];
  creditNotes?: CreditNoteAttributes[];

  createdAt: Date;
  updatedAt: Date;
}

class Invoice extends Document {
  public invoiceNumber!: string;
  public id!: string;

  static associate(models: Models) {
    Invoice.hasMany(models.Receipt, {
      foreignKey: 'invoiceId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Invoice.hasMany(models.CreditNote, {
      foreignKey: 'invoiceId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Invoice.belongsTo(models.Document, {
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Invoice.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    invoiceNumber: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    docId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'documents', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    dateIssued: { type: DataTypes.DATE, allowNull: true },
    dueDate: { type: DataTypes.DATE, allowNull: true },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    modelName: 'Invoice',
    tableName: 'invoices',
  }
);

export default Invoice;
