import { AXIOS_INSTANCES, MICROSERVICES } from '../helpers/axios.helpers';
import { catchError } from '../utilities/catch-async-error';

export default class NotificationAPI {
  private static notificationAxios = AXIOS_INSTANCES.notification;
  private static jobAxios = AXIOS_INSTANCES.job;

  public static sendNotification = catchError(async (payload) => {
    const urlPath = `${MICROSERVICES.notification.routes.notification}/send`;
    console.log({ payload, urlPath }, MICROSERVICES.notification.url);
    const response = await this.notificationAxios.post(urlPath, payload, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });

  public static sendJob = catchError(async (payload) => {
    const urlPath = `${MICROSERVICES.job.routes.job}/send`;
    const response = await this.jobAxios.post(urlPath, payload, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });

  public static updateJob = catchError(async (payload) => {
    const { id, ...rest } = payload;
    const urlPath = `${MICROSERVICES.job.routes.job}/${id}`;
    const response = await this.jobAxios.patch(urlPath, rest, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });
}
