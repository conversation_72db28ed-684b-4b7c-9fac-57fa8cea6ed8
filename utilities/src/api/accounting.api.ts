import { catchError } from '../utilities/catch-async-error';
import { AXIOS_INSTANCES, MICROSERVICES } from '../helpers/axios.helpers';

export default class AccountingAPI {
  private static paymentAxios = AXIOS_INSTANCES.accounting;

  public static syncTransaction = catchError(async (payload: { orgId: string; bankId: string }) => {
    const urlPath = `${MICROSERVICES.accounting.routes.system}/transactions/${payload.bankId}/sync`;
    const response = await this.paymentAxios.get(urlPath, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_API_ACCESS_KEY,
      },
      params: { orgId: payload.orgId, requestBy: 'system' },
    });
    return response.data.data;
  });

  public static syncBankDetails = catchError(
    async (args: {
      payload: {
        status: string;
        orgId: string;
        jobId: string;
        reAuth: boolean;
        bankAccountBalance: number;
        bankName: string;
        bankAccountNumber: string;
      };
      bankAccountId: string;
    }) => {
      const urlPath = `${MICROSERVICES.accounting.routes.system}/banks/${args.bankAccountId}/sync`;
      const response = await this.paymentAxios.post(urlPath, args.payload, {
        headers: {
          accept: 'application/json',
          'dgt-access-key': process.env.DGT_API_ACCESS_KEY,
        },
      });
      return response.data.data;
    }
  );

  public static disconnectBank = catchError(async (payload: { orgId: string; bankId: string }) => {
    const urlPath = `${MICROSERVICES.accounting.routes.system}/banks/${payload.bankId}/disconnect`;
    const response = await this.paymentAxios.delete(urlPath, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_API_ACCESS_KEY,
      },
      params: { orgId: payload.orgId },
    });
    return response.data.data;
  });
}
