import os from 'os';
export const adjustPrefetch = async (prefetchCount: number) => {
  const cpuLoad = os.loadavg()[0];
  const cpuCores = os.cpus().length;
  const loadPercentage = (cpuLoad / cpuCores) * 100;

  if (loadPercentage < 50 && prefetchCount < 100) {
    prefetchCount += 10;
  } else if (loadPercentage > 80 && prefetchCount > 10) {
    prefetchCount -= 10;
  }

  return prefetchCount;
};
