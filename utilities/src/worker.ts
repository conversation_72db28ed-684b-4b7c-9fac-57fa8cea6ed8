import dotenv from 'dotenv';

process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production'
  ? dotenv.config()
  : dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true, encoding: 'utf8' });

import BackgroundTaskManager from './utilities/background-tasks/background-tasks-manager.utility';
import logger from './utilities/logger';

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  logger.error('Unhandled Rejection', { reason, promise });

  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  logger.error('Uncaught Exception', { error });

  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

const worker = async () => {
  try {
    const taskManager = new BackgroundTaskManager();
    console.log('worker');
    await taskManager.runTasks();
  } catch (error) {
    console.error('Worker error:', error);
    logger.error('Worker failed', { error });
    throw error;
  }
};

worker().catch((error) => {
  console.error('Worker execution failed:', error);
  process.exit(1);
});
