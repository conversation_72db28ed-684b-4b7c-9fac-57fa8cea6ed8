import dotenv from 'dotenv';

process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production'
  ? dotenv.config()
  : dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true, encoding: 'utf8' });

import app from './app';
import sequelize, { connectDb } from './config/database/connection';
import logger from './utilities/logger';

const port = process.env.PORT || 9999;

const server = app.listen(port, async () => {
  const dbIsConnected = await connectDb();
  logger.info({
    env: process.env.NODE_ENV,
    envFile: process.env.NODE_ENV === 'production' ? '.env' : `${process.env.NODE_ENV}.env`,
    db: dbIsConnected ? 'connected and synced' : 'not connected',
    serverName: 'Utilities Microservice',
    port,
  });
});

process.on('unhandledRejection', async (err: any) => {
  server.close(async () => {
    logger.error({
      name: 'UNHANDLED REJECTION occurred!',
      message: 'Database connection closed and Server shutdown gracefully.',
      err,
    });
    if (sequelize) await sequelize.close();
    process.exit(1);
  });
});

process.on('uncaughtException', async (err: any) => {
  server.close(async () => {
    logger.error({
      name: 'UNCAUGHT EXCEPTION occurred!',
      message: 'Database connection closed and Server shutdown gracefully.',
      err,
    });
    if (sequelize) await sequelize.close();
    process.exit(1);
  });
});

process.on('SIGTERM', async () => {
  server.close(async () => {
    logger.info({
      name: 'SIGTERM received!',
      message: 'Database connection closed and Server shutdown gracefully.',
    });
    if (sequelize) await sequelize.close();
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  server.close(async () => {
    logger.info({
      name: 'SIGINT received!',
      message: 'Database connection closed and Server shutdown gracefully.',
    });
    if (sequelize) await sequelize.close();
    process.exit(0);
  });
});
