import amqplib, { Channel, Connection, ConsumeMessage, Options } from 'amqplib';
import logger from '../logger';
import DocumentAPI from '../../api/document.api';
import { handleEmailAttachments } from '../global.utilities';
import EmailServices from '../../services/email-sender.service';
import { isTestEnv } from '../guards';
import PaymentAPI from '../../api/payment.api';
import { VALID_QUEUE_NAMES_ARRAY, VALID_QUEUES } from '../../constants/values.constants';
import { adjustPrefetch } from '../../helpers/message-brokers.helpers';
import AccountingAPI from '../../api/accounting.api';
import { v4 as uuidv4 } from 'uuid';
import { QueueConfig, RetryConfig } from '../../types/message-brokers.types';
import { CircuitBreaker } from '../circuit-breaker.utilities';
import NotificationAPI from '../../api/notification';
import { LogsAttributes } from '../../interfaces/models/logs.model.interfaces';
import LogServices from '../../services/log.service';

export default class MessageBrokers {
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private logServices: LogServices;
  private emailServices: EmailServices;
  private readonly queues = VALID_QUEUES;
  private isShuttingDown = false;
  private reconnectionAttempts = 0;
  private readonly maxReconnectionAttempts = 5;
  private readonly baseReconnectDelayMs = 1000;
  private readonly queueConfig: QueueConfig = {
    // ttlMs: ********, // 1 day
    // dlqTtlMs: *********, // 7 days
    prefetchCount: 50,
  };
  private readonly retryConfig: RetryConfig = {
    maxRetries: 3,
    retryDelayMs: 5000,
  };
  private readonly circuitBreaker: CircuitBreaker;
  private readonly apiCircuitBreakers: Map<string, CircuitBreaker>;
  private processedMessages = new Set<string>();

  constructor() {
    this.logServices = new LogServices();
    this.emailServices = new EmailServices();
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 3,
      resetTimeoutMs: 30000, // 30 seconds
      halfOpenMaxRetries: 1,
    });
    this.apiCircuitBreakers = new Map([
      [
        VALID_QUEUES.verifySubscription,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.syncBankDetails,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.userBankDisconnected,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.syncTransaction,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.verifyTransaction,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.processReceipt,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.sendEmailNotification,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.appNotification,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.saveRequestLogs,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.addJob,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
      [
        VALID_QUEUES.updateJob,
        new CircuitBreaker({ failureThreshold: 3, resetTimeoutMs: 15000, halfOpenMaxRetries: 1 }),
      ],
    ]);
  }

  private validateQueueName(queue: string): boolean {
    return VALID_QUEUE_NAMES_ARRAY.includes(queue);
  }

  private async checkConnectionState(): Promise<boolean> {
    if (!this.connection || !this.channel || this.isShuttingDown) {
      logger.warn('Connection or channel unavailable, attempting reconnect...');
      await this.connect();
      return !!this.channel;
    }
    return true;
  }

  public async connect() {
    if (this.connection && this.channel && !this.isShuttingDown) return true;

    return this.circuitBreaker.execute(async () => {
      this.reconnectionAttempts++;
      if (this.reconnectionAttempts > this.maxReconnectionAttempts) {
        logger.error('Max reconnection attempts reached. Aborting.');
        throw new Error('Max reconnection attempts reached');
      }

      this.connection = await amqplib.connect(
        isTestEnv ? 'amqp://localhost' : process.env.RABBITMQ_URL!,
        { heartbeat: 30, timeout: 10000 }
      );
      this.channel = await this.connection.createChannel();

      for (const queue of VALID_QUEUE_NAMES_ARRAY) {
        const queueOptions: Options.AssertQueue = {
          durable: true,
          arguments: {
            'x-dead-letter-exchange': '',
            'x-dead-letter-routing-key': `${queue}DLQ`,
            // 'x-message-ttl': this.queueConfig.ttlMs,
          },
        };
        const dlqOptions: Options.AssertQueue = {
          durable: true,
          //   arguments: { 'x-message-ttl': this.queueConfig.dlqTtlMs },
        };
        await this.channel.assertQueue(`${queue}DLQ`, dlqOptions);
        await this.channel.assertQueue(queue, queueOptions);
      }
      await this.channel.prefetch(this.queueConfig.prefetchCount);

      setInterval(async () => {
        if (this.isShuttingDown || !this.channel) return;
        try {
          const newCount = await adjustPrefetch(this.queueConfig.prefetchCount);
          await this.channel.prefetch(newCount);
          logger.info(`Adjusted prefetch count to: ${newCount}`);
        } catch (error) {
          logger.error('Error adjusting prefetch:', error.message);
        }
      }, 10000);

      this.connection.on('close', () => {
        if (!this.isShuttingDown) {
          logger.warn('RabbitMQ connection closed. Reconnecting...');
          this.reconnect();
        }
      });

      this.connection.on('error', (err) => {
        if (!this.isShuttingDown) {
          logger.error('RabbitMQ error:', err.message);
          this.reconnect();
        }
      });

      this.connection.on('blocked', (reason) => {
        logger.warn(`Connection blocked: ${reason}`);
      });

      this.connection.on('unblocked', () => {
        logger.info('Connection unblocked');
      });

      this.reconnectionAttempts = 0;
      logger.info('RabbitMQ connected successfully');
      this.performHealthCheck();

      return true;
    }, 'connect');
  }

  private async reconnect() {
    if (this.isShuttingDown) return;
    this.connection = null;
    this.channel = null;
    const delay = Math.min(
      this.baseReconnectDelayMs * Math.pow(2, this.reconnectionAttempts),
      30000
    );
    logger.info(`Reconnecting in ${delay}ms (attempt ${this.reconnectionAttempts})`);
    await new Promise((resolve) => setTimeout(resolve, delay));
    await this.connect();
  }

  private async publishMessage(queue: string, data) {
    if (!this.validateQueueName(queue)) {
      logger.error(`Invalid queue name: ${queue}`);
      throw new Error(`Invalid queue name: ${queue}`);
    }

    if (!(await this.checkConnectionState())) {
      throw new Error('No active RabbitMQ connection');
    }

    return this.circuitBreaker.execute(async () => {
      const queueMsg = Buffer.from(JSON.stringify(data), 'utf8');
      const sent = this.channel!.sendToQueue(queue, queueMsg, {
        persistent: true,
        messageId: data.uid,
        headers: { 'x-retry-count': 0 },
      });
      if (!sent) {
        logger.warn(`Failed to publish message to ${queue}`);
        throw new Error(`Failed to publish message to ${queue}`);
      }
      logger.info(`Published message ${data.uid} to ${queue}`);
      return true;
    }, `publishMessage-${queue}`);
  }

  private processMessage(msg: ConsumeMessage) {
    try {
      const data = JSON.parse(msg.content.toString());
      if (!data.uid || !data.timestamp) {
        throw new Error('Invalid message format');
      }
      return data;
    } catch (error) {
      logger.error('Failed to parse message:', error.message);
      throw error;
    }
  }

  public async addTasks(data, queue: string) {
    const message = {
      ...data,
      uid: uuidv4(),
      timestamp: Date.now(),
    };
    return await this.publishMessage(queue, message);
  }

  private async subscribeToQueue(queue: string, handler: (data) => Promise<void>) {
    if (!this.validateQueueName(queue)) {
      logger.error(`Invalid queue name: ${queue}`);
      return;
    }

    if (!(await this.checkConnectionState())) {
      logger.error(`Failed to subscribe to ${queue}: No connection`);
      return;
    }

    logger.info(`Subscribing to queue: ${queue}`);

    await this.channel!.consume(
      queue,
      async (msg: ConsumeMessage | null) => {
        if (!msg || this.isShuttingDown) return;
        const startTime = Date.now();
        try {
          const data = this.processMessage(msg);
          const { uid, timestamp, ...rest } = data;
          if (this.processedMessages.has(uid)) {
            logger.warn(`Duplicate message ${uid} detected, skipping`);
            this.channel!.ack(msg);
            return;
          }

          this.processedMessages.add(uid);
          await handler(rest);
          this.channel!.ack(msg);
          logger.info(
            `Processed message ${uid} from ${queue} create at ${timestamp} in ${Date.now() - startTime}ms`
          );

          if (this.processedMessages.size > 10000) {
            this.processedMessages.clear();
          }
        } catch (error) {
          logger.error(`Error processing message from ${queue}:`, error);
          const retryCount = Number(msg.properties.headers?.['x-retry-count'] || 0);
          if (retryCount < this.retryConfig.maxRetries) {
            setTimeout(() => {
              this.channel!.sendToQueue(queue, msg.content, {
                ...msg.properties,
                headers: { 'x-retry-count': retryCount + 1 },
              });
              this.channel!.ack(msg);
            }, this.retryConfig.retryDelayMs);
          } else {
            this.channel!.sendToQueue(`${queue}DLQ`, msg.content, msg.properties);
            this.channel!.ack(msg);
            logger.warn(`Moved message to ${queue}.dlq after ${retryCount} retries`);
          }
        }
      },
      { noAck: false }
    );
  }

  public async processTasks() {
    for (const queue of VALID_QUEUE_NAMES_ARRAY) {
      logger.info(`Processing queue: ${queue}`);
      try {
        const apiCircuitBreaker = this.apiCircuitBreakers.get(queue);
        if (!apiCircuitBreaker) {
          logger.error(`No circuit breaker configured for queue ${queue}`);
          continue;
        }

        if (queue === VALID_QUEUES.verifySubscription) {
          await this.subscribeToQueue(this.queues.verifySubscription, async (data) => {
            await apiCircuitBreaker.execute(
              () => PaymentAPI.verifySubscription(data),
              'PaymentAPI.verifySubscription'
            );
          });
        }
        if (queue === VALID_QUEUES.syncBankDetails) {
          await this.subscribeToQueue(this.queues.syncBankDetails, async (data) => {
            await apiCircuitBreaker.execute(
              () =>
                AccountingAPI.syncBankDetails({
                  bankAccountId: data.bankAccountId,
                  payload: {
                    orgId: data.orgId,
                    jobId: data.jobId,
                    ...data.metadata,
                  },
                }),
              'AccountingAPI.syncBankDetails'
            );
          });
        }
        if (queue === VALID_QUEUES.userBankDisconnected) {
          await this.subscribeToQueue(this.queues.userBankDisconnected, async (data) => {
            await apiCircuitBreaker.execute(
              () => AccountingAPI.disconnectBank({ orgId: data.orgId, bankId: data.bankId }),
              'AccountingAPI.disconnectBank'
            );
          });
        }
        if (queue === VALID_QUEUES.syncTransaction) {
          await this.subscribeToQueue(this.queues.syncTransaction, async (data) => {
            await apiCircuitBreaker.execute(
              () => AccountingAPI.syncTransaction({ orgId: data.orgId, bankId: data.bankId }),
              'AccountingAPI.syncTransaction'
            );
          });
        }
        if (queue === VALID_QUEUES.verifyTransaction) {
          await this.subscribeToQueue(this.queues.verifyTransaction, async (data) => {
            await apiCircuitBreaker.execute(
              () => PaymentAPI.verifyTransaction(data),
              'PaymentAPI.verifyTransaction'
            );
          });
        }
        if (queue === VALID_QUEUES.appNotification) {
          await this.subscribeToQueue(this.queues.appNotification, async (data) => {
            console.log({ notification: data });
            await apiCircuitBreaker.execute(
              () => NotificationAPI.sendNotification(data),
              'NotificationAPI.sendNotification'
            );
          });
        }
        if (queue === VALID_QUEUES.addJob) {
          await this.subscribeToQueue(this.queues.addJob, async (data) => {
            await apiCircuitBreaker.execute(
              () => NotificationAPI.sendJob(data),
              'NotificationAPI.addJobQueue'
            );
          });
        }
        if (queue === VALID_QUEUES.updateJob) {
          await this.subscribeToQueue(this.queues.updateJob, async (data) => {
            await apiCircuitBreaker.execute(
              () => NotificationAPI.updateJob(data),
              'NotificationAPI.updateJobQueue'
            );
          });
        }
        if (queue === VALID_QUEUES.processReceipt) {
          await this.subscribeToQueue(this.queues.processReceipt, async (data) => {
            await apiCircuitBreaker.execute(
              () => DocumentAPI.systemCreateReceipt(data),
              'DocumentAPI.systemCreateReceipt'
            );
          });
        }
        if (queue === VALID_QUEUES.sendEmailNotification) {
          await this.subscribeToQueue(this.queues.sendEmailNotification, async (data) => {
            await apiCircuitBreaker.execute(async () => {
              if (!data.from && !process.env.NO_REPLY_EMAIL_USERNAME) {
                throw new Error('No sender email configured');
              }
              const formData = handleEmailAttachments({
                ...data,
                from: data.from || process.env.NO_REPLY_EMAIL_USERNAME,
              });
              const transporter = await this.emailServices.processEmail(formData);
              if (!transporter || typeof transporter.sendMail !== 'function') {
                throw new Error('Invalid transporter instance');
              }
              await transporter.sendMail(formData);
            }, 'EmailServices.sendEmail');
          });
        }
        if (queue === VALID_QUEUES.saveRequestLogs) {
          await this.subscribeToQueue(
            this.queues.saveRequestLogs,
            async (data: Partial<LogsAttributes>) => {
              await apiCircuitBreaker.execute(async () => {
                await this.logServices.saveLog(data);
              }, 'saveRequestLogs');
            }
          );
        }
      } catch (error) {
        logger.error(`Error processing queue ${queue}:`, error.message);
      }
    }
  }

  public async requeueMessages(dlq: string, mainQueue: string) {
    if (!this.validateQueueName(mainQueue)) {
      logger.error(`Invalid main queue name: ${mainQueue}`);
      throw new Error(`Invalid main queue name: ${mainQueue}`);
    }

    if (!(await this.checkConnectionState())) {
      throw new Error('No active RabbitMQ connection');
    }

    logger.info(`Moving messages from ${dlq} to ${mainQueue}`);
    let requeuedCount = 0;

    await this.channel!.consume(
      dlq,
      (msg) => {
        if (!msg || this.isShuttingDown) return;
        this.channel!.sendToQueue(mainQueue, msg.content, {
          ...msg.properties,
          headers: { ...msg.properties.headers, 'x-requeued-at': Date.now() },
        });
        this.channel!.ack(msg);
        requeuedCount++;
        logger.info(`Requeued message ${msg.properties.messageId} from ${dlq} to ${mainQueue}`);
      },
      { noAck: false }
    );

    return requeuedCount;
  }

  public async getQueueStats(queue: string) {
    if (!this.validateQueueName(queue)) {
      throw new Error(`Invalid queue name: ${queue}`);
    }

    if (!(await this.checkConnectionState())) {
      throw new Error('No active RabbitMQ connection');
    }

    const stats = await this.channel!.checkQueue(queue);
    return {
      queue,
      messageCount: stats.messageCount,
      consumerCount: stats.consumerCount,
    };
  }

  private async performHealthCheck() {
    setInterval(async () => {
      if (this.isShuttingDown || !(await this.checkConnectionState())) return;
      try {
        const stats = await this.getQueueStats(VALID_QUEUES.verifySubscription);
        const apiBreakerStates = Array.from(this.apiCircuitBreakers.entries()).map(
          ([queue, breaker]) => `${queue}: ${breaker.getState()}`
        );
        logger.info(`Health check: ${JSON.stringify(stats)}`);
        logger.info(`RabbitMQ circuit breaker state: ${this.circuitBreaker.getState()}`);
        logger.info(`API circuit breaker states: ${apiBreakerStates.join(', ')}`);
      } catch (error) {
        logger.error('Health check failed:', error.message);
        await this.reconnect();
      }
    }, 30000);
  }

  public async gracefulShutdown() {
    this.isShuttingDown = true;
    logger.info('Initiating graceful shutdown...');
    try {
      if (this.channel) await this.channel.close();
      if (this.connection) await this.connection.close();
      this.channel = null;
      this.connection = null;
      logger.info('RabbitMQ connection closed gracefully');
    } catch (error) {
      logger.error('Error during graceful shutdown:', error.message);
    }
  }
}
