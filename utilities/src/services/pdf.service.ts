import { Request } from 'express';
import puppeteer from 'puppeteer';
import logger from '../utilities/logger';
import { isTestEnv } from '../utilities/guards';
import { DOCUMENT_PDF_TYPES } from '../constants/values.constants';

export default class PDFServices {
  async getBrowserInstance() {
    if (isTestEnv) {
      return puppeteer.launch({ headless: true });
    } else {
      return puppeteer.launch({
        headless: true,
        executablePath: '/usr/bin/chromium-browser',
        args: ['--no-sandbox', '--headless', '--disable-gpu', '--disable-dev-shm-usage'],
      });
    }
  }

  async generatePayslipPdf(req: Request): Promise<Buffer> {
    const data = { ...(req.body as { bodyHtml: string; footerHtml: string }) };
    const bodyHtml = data.bodyHtml;
    const footerHtml = data.footerHtml;

    const browser = await this.getBrowserInstance();
    const page = await browser.newPage();

    // Define height constants for multiple page PDFs
    const MULTIPLE_PAGE_PDF_HEIGHT = 1124;
    let SINGLE_PAGE_PDF_HEIGHT = 880;
    const FOOTER_HEIGHT = 297;

    try {
      if (!footerHtml.includes('Bank name') && !footerHtml.includes('Account number')) {
        SINGLE_PAGE_PDF_HEIGHT = 970;
      } else {
        SINGLE_PAGE_PDF_HEIGHT = 880;
      }

      await page.setContent(bodyHtml, {
        waitUntil: 'networkidle0',
        timeout: 60000,
      });

      const contentHeight = await page.evaluate(() => {
        return document.body.clientHeight;
      });

      // Calculate the total number of pages needed
      const totalPages = Math.ceil(contentHeight / SINGLE_PAGE_PDF_HEIGHT);

      // Calculate the height of the content on the last page
      const lastPageContentHeight = contentHeight % MULTIPLE_PAGE_PDF_HEIGHT;
      const availableSpaceOnLastPage = SINGLE_PAGE_PDF_HEIGHT - lastPageContentHeight;

      let footerPosition;

      if (totalPages <= 1) {
        // Place footer at the bottom of the single-page document
        footerPosition = SINGLE_PAGE_PDF_HEIGHT;
      } else {
        if (availableSpaceOnLastPage >= FOOTER_HEIGHT) {
          footerPosition = contentHeight + availableSpaceOnLastPage;
        } else {
          footerPosition = contentHeight + availableSpaceOnLastPage + MULTIPLE_PAGE_PDF_HEIGHT;
        }
      }

      // Add the footer to the document at the calculated position
      await page.evaluate(
        (footerHtml, footerPosition) => {
          const footer = document.createElement('div');
          footer.innerHTML = footerHtml;
          footer.style.position = 'absolute';
          footer.style.width = '100%';
          footer.style.top = `${footerPosition}px`;
          document.body.appendChild(footer);
        },
        footerHtml,
        footerPosition
      );

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        displayHeaderFooter: false,
      });

      await browser.close();
      return pdfBuffer;
    } catch (error) {
      console.error('Error during PDF generation:', error);
      await browser.close();
    }
  }

  async generateDocumentPdf(req: Request): Promise<Buffer> {
    const documentType = String(req.query.pdfType).split(':')[1];

    const data = { ...(req.body as { bodyHtml: string; footerHtml: string }) };
    const bodyHtml = data.bodyHtml;
    const footerHtml = data.footerHtml;

    const browser = await this.getBrowserInstance();
    const page = await browser.newPage();

    const MULTIPLE_PAGE_PDF_HEIGHT = 1124;

    try {
      await page.setContent(bodyHtml, {
        waitUntil: 'networkidle0',
        timeout: 120000,
      });

      await page.waitForFunction('document.fonts.ready');

      const contentHeight = await page.evaluate(() => {
        return document.body.clientHeight;
      });

      await page.evaluate((footerHtml) => {
        const footer = document.createElement('div');
        footer.innerHTML = footerHtml;
        footer.id = 'footer';
        footer.style.visibility = 'hidden';
        document.body.appendChild(footer);
      }, footerHtml);

      const footerHeightCalc = await page.evaluate(() => {
        const footer = document.getElementById('footer');
        return footer ? footer.clientHeight : 0;
      });

      let footerHeight = 0;
      let singlePagePDFHeight = 0;

      switch (documentType) {
        case DOCUMENT_PDF_TYPES.invoice:
          footerHeight = footerHeightCalc;
          singlePagePDFHeight = 825 - (footerHeightCalc - 297);
          break;
        case DOCUMENT_PDF_TYPES.creditNote:
          footerHeight = footerHeightCalc;
          singlePagePDFHeight = 785 - (footerHeightCalc - 334);
          break;
        case DOCUMENT_PDF_TYPES.receipt:
          singlePagePDFHeight = 759;
          footerHeight = 306;
          break;
        default:
          break;
      }

      await page.evaluate(() => {
        const footer = document.getElementById('footer');
        if (footer) footer.remove();
      });

      const totalPages = Math.ceil(contentHeight / singlePagePDFHeight);

      const lastPageContentHeight = contentHeight % MULTIPLE_PAGE_PDF_HEIGHT;
      const availableSpaceOnLastPage = singlePagePDFHeight - lastPageContentHeight;

      let footerPosition: number;

      if (totalPages <= 1) {
        footerPosition = singlePagePDFHeight;
      } else {
        if (availableSpaceOnLastPage >= footerHeight) {
          footerPosition = contentHeight + availableSpaceOnLastPage;
        } else {
          footerPosition = contentHeight + availableSpaceOnLastPage + MULTIPLE_PAGE_PDF_HEIGHT;
        }
      }

      await page.evaluate(
        (footerHtml, footerPosition) => {
          const footer = document.createElement('div');
          footer.innerHTML = footerHtml;
          footer.style.position = 'absolute';
          footer.style.width = '100%';
          footer.style.top = `${footerPosition}px`;

          document.body.appendChild(footer);
        },
        footerHtml,
        footerPosition
      );
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        displayHeaderFooter: false,
        waitForFonts: true,
      });

      await browser.close();
      return pdfBuffer;
    } catch (error) {
      logger.error('Error during PDF generation:', error);
      await browser.close();
      throw error;
    }
  }
}
