import { Request, Response } from 'express';
import { RESPONSES } from '../constants/responses.constants';
import { NotFoundError } from '../helpers/error.helpers';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { formatDateToYearMonthDayTime, getUserTimeZone } from '../utilities/global.utilities';
import RequestIP from 'request-ip';
import { successResponse } from '../helpers/response.helpers';

export default class UtilityController extends RequestHandlerErrorWrapper {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async resourceNotFound(req: Request, res: Response) {
    throw new NotFoundError(
      `${req.method} not allowed for ${req.originalUrl} OR, requested resource is not available`
    );
  }

  async getServerHealth(req: Request, res: Response) {
    const data = {
      time: formatDateToYearMonthDayTime(new Date(), getUserTimeZone(req)),
      ipAddress: RequestIP.getClientIp(req),
      timezone: getUserTimeZone(req),
      device: req.headers['user-agent'],
    };
    return successResponse(res, RESPONSES.serverIsActive, 'get server health', data);
  }

  async getAPIDocumentation(req: Request, res: Response) {
    if (process.env.API_DOCS) return res.redirect(process.env.API_DOCS);
    else throw new NotFoundError('documentation not published yet, kindly contact the developers');
  }
}
