import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import AuthServices from '../services/auth.service';
import { LoginPayload } from '../interfaces/auth.interfaces';

export default class AuthControllers extends RequestHandlerErrorWrapper {
  constructor(private authServices: AuthServices) {
    super();
  }

  async login(req: Request, res: Response) {
    const payload = req.body as LoginPayload;
    return res.json(await this.authServices.login(payload)).end();
  }
}
