import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import SubscriptionPlanServices from '../services/subscription-plan.service';
import { sendAdminResponse } from '../helpers/response.helpers';
import { getPagination } from '../utilities/global.utilities';
import { SubscriptionPlanPayload } from '../interfaces/subscription-plan.interfaces';

export default class SubscriptionPlanControllers extends RequestHandlerErrorWrapper {
  constructor(private subscriptionPlanServices: SubscriptionPlanServices) {
    super();
  }

  async getAllSubscriptionPlans(req: Request, res: Response) {
    const { limit, page } = getPagination(req);
    const { status, data } = await this.subscriptionPlanServices.getAllSubscriptionPlans(
      page,
      limit
    );

    return sendAdminResponse(res, status, data);
  }

  async createSubscriptionPlan(req: Request, res: Response) {
    const payload = req.body as SubscriptionPlanPayload[];
    const { status, data } = await this.subscriptionPlanServices.createSubscriptionPlan(payload);
    return sendAdminResponse(res, status, data);
  }

  async editSubscriptionPlan(req: Request, res: Response) {
    const { planId } = req.params;
    const payload = req.body as SubscriptionPlanPayload;

    const { status, data } = await this.subscriptionPlanServices.editSubscriptionPlan(
      planId,
      payload
    );

    return sendAdminResponse(res, status, data);
  }

  async deleteSubscriptionPlan(req: Request, res: Response) {
    const { planId } = req.params;
    const { status, data } = await this.subscriptionPlanServices.deleteSubscriptionPlan(planId);

    return sendAdminResponse(res, status, data);
  }
}
