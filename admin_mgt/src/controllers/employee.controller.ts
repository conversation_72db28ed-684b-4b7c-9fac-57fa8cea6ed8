import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import EmployeeServices from '../services/employee.service';
import { sendAdminResponse } from '../helpers/response.helpers';
import { getPagination } from '../utilities/global.utilities';

export default class EmployeeControllers extends RequestHandlerErrorWrapper {
  constructor(private employeeServices: EmployeeServices) {
    super();
  }

  async getAllOrganizationEmployees(req: Request, res: Response) {
    const { page, limit } = getPagination(req);
    const { organizationId } = req.params;

    const { status, data } = await this.employeeServices.getAllOrganizationEmployee(
      organizationId,
      page,
      limit
    );

    return sendAdminResponse(res, status, data);
  }

  async getOneOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const { status, data } = await this.employeeServices.getOneOrganizationEmployee(
      organizationId,
      employeeId
    );

    return sendAdminResponse(res, status, data);
  }

  async getAllLeavesForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const { limit, page } = getPagination(req);

    const { status, data } = await this.employeeServices.getAllLeavesForOrganizationEmployee(
      organizationId,
      employeeId,
      page,
      limit
    );

    return sendAdminResponse(res, status, data);
  }

  async getOneLeaveForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId, leaveId } = req.params;
    const { status, data } = await this.employeeServices.getOneLeaveForOrganizationEmployee(
      organizationId,
      employeeId,
      leaveId
    );

    return sendAdminResponse(res, status, data);
  }

  async getAllPaymentHistoryForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const { limit, page } = getPagination(req);

    const { status, data } =
      await this.employeeServices.getAllPaymentHistoryForOrganizationEmployee(
        organizationId,
        employeeId,
        page,
        limit
      );

    return sendAdminResponse(res, status, data);
  }

  async getOnePaymentHistoryForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId, paymentHistoryId } = req.params;
    const { status, data } =
      await this.employeeServices.getOnePaymentHistoryForOrganizationEmployee(
        organizationId,
        employeeId,
        paymentHistoryId
      );

    return sendAdminResponse(res, status, data);
  }
}
