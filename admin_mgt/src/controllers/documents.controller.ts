import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import DocumentServices from '../services/document.service';
import { getPagination } from '../utilities/global.utilities';
import { sendAdminResponse } from '../helpers/response.helpers';
import { DocumentFilter } from '../types';

export default class DocumentControllers extends RequestHandlerErrorWrapper {
  constructor(private documentServices: DocumentServices) {
    super();
  }

  async getAllOrganizationDocuments(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { limit, page } = getPagination(req);

    const { status, data } = await this.documentServices.getAllOrganizationDocuments(
      organizationId,
      page,
      limit
    );

    return sendAdminResponse(res, status, data);
  }

  async getFilteredOrganizationDocuments(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { page, limit } = getPagination(req);
    const filter = req.query as DocumentFilter;

    const { status, data } = await this.documentServices.getFilteredOrganizationDocuments(
      organizationId,
      filter,
      page,
      limit
    );

    return sendAdminResponse(res, status, data);
  }

  async getAllArchivedDocuments(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { limit, page } = getPagination(req);
    const { status, data } = await this.documentServices.getAllArchivedOrganizationDocuments(
      organizationId,
      page,
      limit
    );

    return sendAdminResponse(res, status, data);
  }
}
