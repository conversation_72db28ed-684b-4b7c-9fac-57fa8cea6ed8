import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import UserSubscriptionServices from '../services/user-subscription.service';
import { sendAdminResponse } from '../helpers/response.helpers';
import { getPagination } from '../utilities/global.utilities';
import { UserSubscriptionsFilter } from '../types';

export default class UserSubscriptionControllers extends RequestHandlerErrorWrapper {
  constructor(private userSubscriptionServices: UserSubscriptionServices) {
    super();
  }

  async getUserSubscriptionHistories(req: Request, res: Response) {
    const { startDate, endDate } = req.query as UserSubscriptionsFilter;
    const { limit, page } = getPagination(req);

    const { status, data } = await this.userSubscriptionServices.getUserSubscriptions(
      startDate,
      endDate,
      page,
      limit
    );

    return sendAdminResponse(res, status, data);
  }
}
