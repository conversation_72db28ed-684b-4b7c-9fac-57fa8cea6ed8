import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import DashboardServices from '../services/dashboard.service';
import { TimeFrameFilterType } from '../types';
import { sendAdminResponse } from '../helpers/response.helpers';

export default class DashboardControllers extends RequestHandlerErrorWrapper {
  constructor(private dashboardServices: DashboardServices) {
    super();
  }

  async getOverview(req: Request, res: Response) {
    const { period } = req.query as { period: TimeFrameFilterType };
    const data = await this.dashboardServices.getOverview(period);
    return sendAdminResponse(res, 200, {
      message: 'Dashboard overview retrieved successfully',
      code: 'S200',
      data,
    });
  }
}
