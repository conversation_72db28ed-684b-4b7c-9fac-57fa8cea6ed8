import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import UserServices from '../services/users.service';
import { getPagination } from '../utilities/global.utilities';
import { sendAdminResponse } from '../helpers/response.helpers';

export default class UserControllers extends RequestHandlerErrorWrapper {
  constructor(private userServices: UserServices) {
    super();
  }

  async getAllUsers(req: Request, res: Response) {
    const { page, limit } = getPagination(req);
    const { status, data } = await this.userServices.getAllUsers(page, limit);
    return sendAdminResponse(res, status, data);
  }

  async getOneUser(req: Request, res: Response) {
    const { userId } = req.params;
    const { status, data } = await this.userServices.getOneUser(userId);
    return sendAdminResponse(res, status, data);
  }
}
