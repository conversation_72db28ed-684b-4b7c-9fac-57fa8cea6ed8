import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import OrganizationServices from '../services/organization.service';
import { sendAdminResponse } from '../helpers/response.helpers';
import { getPagination } from '../utilities/global.utilities';

export default class OrganizationControllers extends RequestHandlerErrorWrapper {
  constructor(private organizationServices: OrganizationServices) {
    super();
  }

  async getAllOrganizations(req: Request, res: Response) {
    const { page, limit } = getPagination(req);
    const { status, data } = await this.organizationServices.getAllOrganizations(page, limit);
    return sendAdminResponse(res, status, data);
  }

  async getOneOrganizations(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { status, data } = await this.organizationServices.getOneOrganization(organizationId);
    return sendAdminResponse(res, status, data);
  }
}
