import axios, { AxiosResponse, AxiosError } from 'axios';
import { AppError, InternalServerError } from '../../helpers/error.helpers';
import { DEFINED_MS_ERROR_CODES_ARRAY } from '../../constants/values.constants';
import logger from '../../utilities/logger';

const axiosInstance = axios.create();

axiosInstance.interceptors.request.use(
  async (config) => {
    // log the request details with a redacted auth.
    logger.info('Axios Request', {
      name: 'Axios Request',
      method: config.method,
      url: `${config.baseURL ?? ''}${config.url}`,
      parameters: config.params,
      headers: { ...config.headers, Authorization: '***' },
      timeStamp: new Date().toISOString(),
    });

    return config;
  },

  (error: AxiosError) => {
    // handle request setup error
    logger.error('Axios Request Config Setup Error', {
      name: 'Axios Request Config Setup Error',
      message: error?.message,
      cause: error?.cause,
      stack: error?.stack,
      error,
    });

    return Promise.reject(new InternalServerError());
  }
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    if (error.response) {
      const { status, statusText, data } = error.response;
      const { code, message } = data;
      logger.error(`Response Error: ${status} ${statusText}`, error);

      if (Object.values(DEFINED_MS_ERROR_CODES_ARRAY).includes(code))
        throw new AppError(message, status, 'axios');
      return Promise.reject({
        ...error.response,
        name: error.name,
        message: data?.message || error.message,
        statusCode: status,
      });
    }

    logger.error(`Unknown Error: ${error.message}`);
    return Promise.reject(error);
  }
);

export default axiosInstance;
