import { HTTP_METHODS_ARRAY } from '../constants/values.constants';
import { DOCUMENT_TYPES_ARRAY, TIME_FRAME_FILTER_ARRAY } from '../models/enums';

export type RequestMethods = (typeof HTTP_METHODS_ARRAY)[number];

export type RequestOptions = {
  method: RequestMethods;
  url: string;
  payload?: any;
  params?: Record<string, any>;
  accessKey?: string;
  apiKey?: string;
  authHeader?: boolean;
  reqId?: boolean;
  responseType?: string;
};

export type TimeFrameFilterType = (typeof TIME_FRAME_FILTER_ARRAY)[number];

export type DocumentTypes = (typeof DOCUMENT_TYPES_ARRAY)[number];

export type DocumentFilter = {
  type: DocumentTypes;
};

export type UserSubscriptionsFilter = {
  startDate: string;
  endDate: string;
};
