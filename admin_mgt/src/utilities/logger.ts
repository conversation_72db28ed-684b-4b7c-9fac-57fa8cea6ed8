import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import os from 'os';
import { EnhancedLogger } from '../helpers/logger.helper';

const NODE_ENV = process.env.NODE_ENV || 'development';
const LOG_LEVEL = process.env.LOG_LEVEL || (NODE_ENV === 'production' ? 'info' : 'info');
const LOG_DIR = process.env.LOG_DIR || 'logs';

const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
    return `${timestamp} [${level}]: ${stack || message}${metaStr}`;
  })
);

const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    return JSON.stringify({
      ...info,
      service: process.env.SERVICE_NAME || 'api',
      version: process.env.APP_VERSION || '1.0.0',
      hostname: process.env.HOSTNAME || os.hostname(),
      pid: process.pid,
    });
  })
);

const createFileTransport = (filename: string, level: string) => {
  return new DailyRotateFile({
    filename: path.join(LOG_DIR, `%DATE%-${filename}`),
    datePattern: 'YYYY-MM-DD',
    level,
    handleExceptions: level === 'error',
    handleRejections: level === 'error',
    maxSize: '20m',
    maxFiles: '14d',
    zippedArchive: NODE_ENV === 'production',
    format: productionFormat,
  });
};

const transports: winston.transport[] = [];

if (NODE_ENV === 'development' || NODE_ENV === 'test') {
  transports.push(
    new winston.transports.Console({
      level: LOG_LEVEL,
      format: developmentFormat,
      handleExceptions: true,
      handleRejections: true,
    })
  );
} else {
  transports.push(
    new winston.transports.Console({
      level: 'error',
      format: productionFormat,
      silent: process.env.DISABLE_CONSOLE_LOGS === 'true',
    })
  );
}

transports.push(
  createFileTransport('combined.log', 'info'),
  createFileTransport('error.log', 'error'),
  createFileTransport('warn.log', 'warn')
);

if (NODE_ENV === 'test') {
  transports.push(createFileTransport('debug.log', 'debug'));
}

const loggerConfig = winston.createLogger({
  level: LOG_LEVEL,
  format: NODE_ENV === 'production' ? productionFormat : developmentFormat,
  transports,
  exitOnError: false,

  // silent: NODE_ENV === 'test',
});

const logger = new EnhancedLogger(loggerConfig);
export default logger;
