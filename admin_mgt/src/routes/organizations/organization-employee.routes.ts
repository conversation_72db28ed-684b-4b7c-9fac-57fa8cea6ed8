import {
  validateRouteIdParameter,
  validateRouteIdParams,
} from '../../middlewares/validators/global.validators';
import { createRouter } from '../route-helper.';
import { RouteParams } from '../../constants/route-params.constants';

const { router: orgEmployeeRouter, controller: orgEmployeeControllers } =
  createRouter('employeeControllers');

orgEmployeeRouter.get('/', orgEmployeeControllers.getAllOrganizationEmployees);

orgEmployeeRouter.get(
  `/:${RouteParams.EMPLOYEE_ID}`,
  validateRouteIdParameter(RouteParams.EMPLOYEE_ID),
  orgEmployeeControllers.getOneOrganizationEmployee
);

orgEmployeeRouter.get(
  `/:${RouteParams.EMPLOYEE_ID}/employee-payments`,
  validateRouteIdParameter(RouteParams.EMPLOYEE_ID),
  orgEmployeeControllers.getAllPaymentHistoryForOrganizationEmployee
);

orgEmployeeRouter.get(
  `/:${RouteParams.EMPLOYEE_ID}/employee-payments/:${RouteParams.PAYMENT_HISTORY_ID}`,
  validateRouteIdParams([RouteParams.EMPLOYEE_ID, RouteParams.PAYMENT_HISTORY_ID]),
  orgEmployeeControllers.getOnePaymentHistoryForOrganizationEmployee
);

orgEmployeeRouter.get(
  `/:${RouteParams.EMPLOYEE_ID}/leaves`,
  validateRouteIdParameter(RouteParams.EMPLOYEE_ID),
  orgEmployeeControllers.getAllLeavesForOrganizationEmployee
);

orgEmployeeRouter.get(
  `/:${RouteParams.EMPLOYEE_ID}/leaves/:${RouteParams.LEAVE_ID}`,
  validateRouteIdParams([RouteParams.EMPLOYEE_ID, RouteParams.LEAVE_ID]),
  orgEmployeeControllers.getOneLeaveForOrganizationEmployee
);

export default orgEmployeeRouter;
