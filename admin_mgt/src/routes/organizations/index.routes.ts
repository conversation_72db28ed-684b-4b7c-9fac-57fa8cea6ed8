import { validateOrganizationId } from '../../middlewares/validators/global.validators';
import { createRouter } from '../route-helper.';
import orgDocumentRouter from './organization-document.routes';
import { RouteParams } from '../../constants/route-params.constants';

const { router: orgMgtRouter, controller: orgMgtControllers } =
  createRouter('organizationControllers');

orgMgtRouter.get('/', orgMgtControllers.getAllOrganizations);

orgMgtRouter.use(
  `/:${RouteParams.ORGANIZATION_ID}/documents`,
  validateOrganizationId(),
  orgDocumentRouter
);

orgMgtRouter.get(
  `/:${RouteParams.ORGANIZATION_ID}`,
  validateOrganizationId(),
  orgMgtControllers.getOneOrganizations
);

export default orgMgtRouter;
