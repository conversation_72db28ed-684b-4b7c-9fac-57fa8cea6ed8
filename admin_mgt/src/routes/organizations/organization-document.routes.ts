import { validateQueryParams } from '../../middlewares/validators/global.validators';
import { documentFilterSchema } from '../../middlewares/validators/schemas/query-params.schema';
import { createRouter } from '../route-helper.';

const { router: orgDocumentRouter, controller: documentControllers } =
  createRouter('documentControllers');

orgDocumentRouter.get(
  '/filter',
  validateQueryParams(documentFilterSchema),
  documentControllers.getFilteredOrganizationDocuments
);
orgDocumentRouter.get('/', documentControllers.getAllOrganizationDocuments);

export default orgDocumentRouter;
