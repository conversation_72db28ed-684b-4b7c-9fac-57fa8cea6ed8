import { validateQueryParams } from '../middlewares/validators/global.validators';
import { timeFrameFilterSchema } from '../middlewares/validators/schemas/query-params.schema';
import { createRouter } from './route-helper.';

const { router: dashboardRouter, controller: dashboardController } =
  createRouter('dashboardControllers');

dashboardRouter.get(
  '/',
  validateQueryParams(timeFrameFilterSchema),
  dashboardController.getOverview
);

export default dashboardRouter;
