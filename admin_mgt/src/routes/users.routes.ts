import { RouteParams } from '../constants/route-params.constants';
import { validateRouteIdParameter } from '../middlewares/validators/global.validators';
import { createRouter } from './route-helper.';

const { router: userMgtRouter, controller: userMgtControllers } = createRouter('userControllers');

userMgtRouter.get('/', userMgtControllers.getAllUsers);

userMgtRouter
  .route(`/:${RouteParams.USER_ID}`)
  .all(validateRouteIdParameter(RouteParams.USER_ID))

  .get(userMgtControllers.getOneUser);

export default userMgtRouter;
