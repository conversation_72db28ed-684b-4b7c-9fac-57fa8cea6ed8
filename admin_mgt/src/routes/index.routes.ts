import { Router } from 'express';
import utilityRouter from './utilities.routes';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';
import { API_VERSION } from '../constants/values.constants';
import userMgtRouter from './users.routes';
import billingMgtRouter from './billings_mgt/index.subscriptions.routes';
import dashboardRouter from './dashboard.routes';
import orgMgtRouter from './organizations/index.routes';
import authRouter from './auth.routes';

const globalRouter = Router();
const auth = middlewares.resolve('authMiddlewares');
const { captureAppDetails } = middlewares.resolve('utilityMiddlewares');

globalRouter.use(captureAppDetails);

globalRouter.use(`${API_VERSION}`, utilityRouter);

globalRouter.use(`${API_VERSION}/auth`, authRouter);

globalRouter.use(auth.checkAuthTokenAndSetAuthHeader.bind(auth));
globalRouter.use(`${API_VERSION}/overview`, dashboardRouter);
globalRouter.use(`${API_VERSION}/users`, userMgtRouter);
globalRouter.use(`${API_VERSION}/organizations`, orgMgtRouter);
globalRouter.use(`${API_VERSION}/billings`, billingMgtRouter);
globalRouter.all('*', controllers.resolve('utilityControllers').resourceNotFound);

export default globalRouter;
