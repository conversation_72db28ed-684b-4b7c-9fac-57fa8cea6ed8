import { validateRequestB<PERSON> } from '../middlewares/validators/global.validators';
import { loginSchema, verifyOtpSchema } from '../middlewares/validators/schemas/request_body/auth.schema';
import { createRouter } from './route-helper.';

const { router: authRouter, controller: authControllers } = createRouter('authControllers');

authRouter.post('/login', validateRequestBody(loginSchema), authControllers.login);

authRouter.post('/verify-email-otp', validateRequestBody(verifyOtpSchema), aut)

export default authRouter;
