import { validateQueryParams } from '../../middlewares/validators/global.validators';
import { userSubscriptionFilterSchema } from '../../middlewares/validators/schemas/query-params.schema';
import { createRouter } from '../route-helper.';

const { router: usersSubMgtRouter, controller: usersSubMgtControllers } = createRouter(
  'userSubscriptionControllers'
);

usersSubMgtRouter.get(
  '/',
  validateQueryParams(userSubscriptionFilterSchema),
  usersSubMgtControllers.getUserSubscriptionHistories
);

export default usersSubMgtRouter;
