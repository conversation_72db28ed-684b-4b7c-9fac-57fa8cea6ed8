import {
  validateRouteIdParameter,
  validateRequestBody,
} from '../../middlewares/validators/global.validators';
import { createRouter } from '../route-helper.';
import { RouteParams } from '../../constants/route-params.constants';
import {
  subscriptionArraySchema,
  subscriptionSchema,
} from '../../middlewares/validators/schemas/request_body/subscription-plan.schemas';

const { router: subPlansMgtRouter, controller: subPlansMgtControllers } = createRouter(
  'subscriptionPlanControllers'
);

subPlansMgtRouter
  .route('/')

  .get(subPlansMgtControllers.getAllSubscriptionPlans)

  .post(
    validateRequestBody(subscriptionArraySchema),
    subPlansMgtControllers.createSubscriptionPlan
  );

subPlansMgtRouter
  .route(`/:${RouteParams.PLAN_ID}`)
  .all(validateRouteIdParameter(RouteParams.PLAN_ID))

  .put(validateRequestBody(subscriptionSchema), subPlansMgtControllers.editSubscriptionPlan)

  .delete(subPlansMgtControllers.deleteSubscriptionPlan);

export default subPlansMgtRouter;
