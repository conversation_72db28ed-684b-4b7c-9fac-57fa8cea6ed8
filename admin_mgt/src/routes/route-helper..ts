import { Router } from 'express';
import { ControllerInstances } from '../containers/container.interfaces';
import controllers from '../containers/controllers.container';

export const createRouter = <C extends keyof ControllerInstances>(controllerName: C) => {
  const router = Router({ mergeParams: true });
  const controller = controllers.resolve(controllerName) as ControllerInstances[C];

  return { router, controller };
};
