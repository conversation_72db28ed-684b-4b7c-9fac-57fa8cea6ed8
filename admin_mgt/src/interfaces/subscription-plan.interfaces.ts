import { MSResponse } from './global.interfaces';

export enum SUBSCRIPTION_STATUS {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  PENDING = 'pending',
  FAILED = 'failed',
  DOWNGRADED = 'downgraded',
  UPGRADED = 'upgraded',
  CANCELLED = 'cancelled',
}

export interface GetAllSubscriptionPlansData {
  id: string;
  name: string;
  description: string;
  features: {
    authorized: string[];
    restricted: string[];
  };
  is_active: boolean;
  max_users: number;
  pricing: {
    country: string;
    currency: string;
    price: string;
    billing_cycle: string;
  }[];
}

export interface GetAllSubscriptionPlansResponse
  extends MSResponse<GetAllSubscriptionPlansData[]> {}

export interface SubscriptionPlanPayload {
  name: string;
  description: string;
  features: {
    authorized: string[];
    restricted: string[];
  };
  pricing: {
    country: string;
    currency: string;
    price: number;
    billing_cycle: 'MONTHLY' | 'ANNUAL';
  }[];
}

export interface CreateSubscriptionPlanResponse extends Omit<MSResponse<any>, 'data'> {}
