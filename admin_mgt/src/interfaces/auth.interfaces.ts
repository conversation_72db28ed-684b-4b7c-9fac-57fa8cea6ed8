export interface LoginPayload {
  email: string;
  password: string;
}

export interface VerifyOtpPayload {
  email: string;
  otp: number;
}

export interface Verify2faOtpPayload extends VerifyOtpPayload {
  action: string;
}

export interface ResendOtpPayload {
  email: string;
}

export interface RefreshAuthTokenPayload {
  refreshToken: string;
}

export interface VerifyBackupPasswordPayload {
  email: string;
  backupPassword: string;
  action: string;
}

export interface EnableGoogleAuthenticatorPayload {
  email: string;
  secretKey: string;
}

export interface CreateBackupPasswordPayload {
  email: string;
  backupPassword: string;
  backupPasswordHint: string;
}
