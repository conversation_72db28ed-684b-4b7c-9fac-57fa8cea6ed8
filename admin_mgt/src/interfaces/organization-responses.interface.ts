import { MSResponse } from './global.interfaces';
import { SUBSCRIPTION_STATUS } from './subscription-plan.interfaces';

interface OrganizationSubscription {
  access: boolean;
  viewOnly: boolean;
  status: SUBSCRIPTION_STATUS;
  expiresAt: string;
}

export interface OrganizationMember {
  id: string;
  organizationId: string;
  userId: string;
  role: string;
  permissions: string[];
  twoFactorEnabled: boolean;
  default: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface OrganizationData {
  id: string;
  name: string;
  industry: string;
  type: string;
  sortCode: string | null;
  logo: string | null;
  bgColor: string;
  font: string;
  logoThumbnail: string | null;
  address: string;
  established: string;
  state: string | null;
  country: string;
  businessNumber: string;
  NIN: string | null;
  role: string;
  currency: string;
  bankName: string;
  bankAccountName: string;
  bankAccountNumber: string;
  companyRegistrationNumber: string | null;
  vatNumber: string | null;
  default: boolean;
  taxNumber: string | null;
  createdAt: string;
  updatedAt: string;
  subscription: OrganizationSubscription;
  members: OrganizationMember;
}

export interface GetOneOrganizationResponse extends MSResponse<OrganizationData> {}

export interface GetManyOrganizationResponse extends MSResponse<OrganizationData[]> {}
