import { MSResponse } from './global.interfaces';
import { SUBSCRIPTION_STATUS } from './subscription-plan.interfaces';

export interface GetAllUserSubscriptionsData {
  id: string;
  orgId: string;
  subscriptionId: string;
  billingCycle: string | null;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  freeTrial: boolean;
  nextBillingDate: string | null;
  jobId: string | null;
  paymentCardBrand: string | null;
  paymentCardLast4: string | null;
  paymentCardExpiryDate: string | null;
  status: SUBSCRIPTION_STATUS;
  current: boolean;
  reference: string | null;
  paymentMethod: string | null;
  invoiceId: string | null;
  reason: string | null;
  recurring: boolean;
  usedFreeTrial: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface GetAllUserSubscriptionResponse extends MSResponse<GetAllUserSubscriptionsData[]> {}

export interface GetUserSubscriptionStatsData {
  totalSubscriptions: number;
  intervals: {
    date: string; // Format: YYYY-MM-DD
    total: number;
  }[];
}

export interface GetUserSubscriptionStatsResponse
  extends MSResponse<GetUserSubscriptionStatsData[]> {}
