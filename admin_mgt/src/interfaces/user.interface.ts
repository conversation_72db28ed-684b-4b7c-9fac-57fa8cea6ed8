import { MSResponse } from './global.interfaces';
import { OrganizationData, OrganizationMember } from './organization-responses.interface';

export interface UserAccountData {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  authenticator: boolean;
  isSuperAdmin: boolean;
  referral: string | null;
  authenticatorBackup: boolean;
  verified: boolean;
  active: boolean;
  lastLoginAt: string | null;
  lastLoginIp: string | null;
  failedLoginAttempts: number | null;
  lockedUntil: string | null;
  passwordChangedAt: string | null;
  createdBy: string | null;
  updatedBy: string | null;
  deletedBy: string | null;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  role: string;
  organization: OrganizationData;
  organizationMembers: OrganizationMember[];
  globalAccess: boolean;
}

export interface AdminAccountData {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  authenticator: boolean;
  isSuperAdmin: boolean;
  referral: string | null;
  authenticatorBackup: boolean;
  verified: boolean;
  active: boolean;
  lastLoginAt: string | null;
  lastLoginIp: string | null;
  failedLoginAttempts: number | null;
  lockedUntil: string | null;
  passwordChangedAt: string | null;
  createdBy: string | null;
  updatedBy: string | null;
  deletedBy: string | null;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  organizations: any[];
  globalAccess: boolean;
}

export interface AccountResponse extends MSResponse<UserAccountData | AdminAccountData> {}

export interface OneUserData {
  id: string;
  email: string;
  firstname: string;
  lastname: string;
  authenticator: boolean;
  authenticatorBackup: boolean;
  active: boolean;
  verified: boolean;
  loggedIn: number;
  organizations: OrganizationData[];
}

export interface ManyUserData {
  id: string;
  email: string;
  firstname: string;
  lastname: string;
  authenticator: boolean;
  authenticatorBackup: boolean;
  active: boolean;
  verified: boolean;
  loggedIn: number;
  createdAt: string;
  updatedAt: string;
  organizations: OrganizationData[];
}

export interface GetOneUserResponse extends MSResponse<OneUserData> {}

export interface GetManyUserResponse extends MSResponse<ManyUserData[]> {}

export interface UserStatsData {
  totalUsers: number;
  intervals: {
    date: string;
    total: number;
  }[];
}

export interface GetUserStatsResponse extends MSResponse<UserStatsData> {}
