import { <PERSON>rrorWrapper } from '../helpers/class.helpers';
import { TimeFrameFilterType } from '../types';
import DocumentServices from './document.service';
import UserSubscriptionServices from './user-subscription.service';
import UserServices from './users.service';

export default class DashboardServices extends ErrorWrapper {
  constructor(
    private userServices: UserServices,
    private documentServices: DocumentServices,
    private userSubscriptionServices: UserSubscriptionServices
  ) {
    super();
  }

  async getOverview(filter: TimeFrameFilterType) {
    const [
      {
        data: { data: userStats },
      },
      {
        data: { data: documentStats },
      },
      {
        data: { data: userSubscriptionStats },
      },
    ] = await Promise.all([
      this.userServices.getUserStats(filter),
      this.documentServices.getDocumentStats(filter),
      this.userSubscriptionServices.getUserSubscriptionStats(filter),
    ]);

    return {
      users: { total: userStats.totalUsers },
      documents: {
        totalInvoiceDocuments: documentStats.overallInvoiceTotals.count,
        totalInvoiceAmount: documentStats.overallInvoiceTotals.totalAmount,
        topGrossingOrganizations: documentStats.top6Organizations,
      },
      subscriptions: { ...userSubscriptionStats },
    };
  }

  // TODO: discuss error handling with chris to know what to do

  // async getOverviewWithErrorHandling(filter: TimeFrameFilterType) {
  //   const [userStatsResult, documentStatsResult, subscriptionStatsResult] =
  //     await Promise.allSettled([
  //       this.userServices.getUserStats(filter),
  //       this.documentServices.getDocumentStats(filter),
  //       this.userSubscriptionServices.getUserSubscriptionStats(filter),
  //     ]);

  //   const userStats =
  //     userStatsResult.status === 'fulfilled' ? userStatsResult.value.data.data : { totalUsers: 0 };
  //   const documentStats =
  //     documentStatsResult.status === 'fulfilled'
  //       ? documentStatsResult.value.data.data
  //       : {
  //           overallInvoiceTotals: { count: 0, totalAmount: 0 },
  //           top6Organizations: [],
  //         };
  //   const userSubscriptionStats =
  //     subscriptionStatsResult.status === 'fulfilled' ? subscriptionStatsResult.value.data.data : {};

  //   return {
  //     userStats: { totalUsers: userStats.totalUsers },
  //     documentStats: {
  //       totalInvoiceCount: documentStats.overallInvoiceTotals.count,
  //       totalInvoiceAmount: documentStats.overallInvoiceTotals.totalAmount,
  //       top6Organizations: documentStats.top6Organizations,
  //     },
  //     userSubscriptionStats,
  //   };
  // }
}
