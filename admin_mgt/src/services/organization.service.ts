import { HTTP_METHODS, AUTH_AND_ACCOUNT_BASE_URL } from '../constants/values.constants';
import {
  GetManyOrganizationResponse,
  GetOneOrganizationResponse,
} from '../interfaces/organization-responses.interface';
import BaseServices from './base.service';

export default class OrganizationServices extends BaseServices {
  constructor() {
    super(`${AUTH_AND_ACCOUNT_BASE_URL}/organizations`);
  }

  async getAllOrganizations(offset = 1, limit = 50) {
    return await this.sendRequest<GetManyOrganizationResponse>({
      method: HTTP_METHODS.GET,
      url: this.getUrl(),
      params: { offset, limit },
    });
  }

  async getOneOrganization(orgId: string) {
    return await this.sendRequest<GetOneOrganizationResponse>({
      method: HTTP_METHODS.GET,
      url: this.getUrl(`/${orgId}`),
    });
  }
}
