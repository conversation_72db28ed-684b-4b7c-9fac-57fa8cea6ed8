import { HTTP_METHODS, AUTH_AND_ACCOUNT_BASE_URL } from '../constants/values.constants';
import {
  CreateSubscriptionPlanResponse,
  GetAllSubscriptionPlansResponse,
  SubscriptionPlanPayload,
} from '../interfaces/subscription-plan.interfaces';
import BaseServices from './base.service';

export default class SubscriptionPlanServices extends BaseServices {
  constructor() {
    super(`${AUTH_AND_ACCOUNT_BASE_URL}/plans`);
  }

  async getAllSubscriptionPlans(offset = 1, limit = 50) {
    return await this.sendRequest<GetAllSubscriptionPlansResponse>({
      method: HTTP_METHODS.GET,
      url: this.getUrl(),
      params: { offset, limit },
    });
  }

  async createSubscriptionPlan(payload: SubscriptionPlanPayload[]) {
    return await this.sendRequest<CreateSubscriptionPlanResponse>({
      method: HTTP_METHODS.POST,
      url: this.getUrl(),
      payload,
    });
  }

  async editSubscriptionPlan(planId: string, payload: SubscriptionPlanPayload) {
    return await this.sendRequest({
      method: HTTP_METHODS.PUT,
      url: this.getUrl(`/${planId}`),
      payload,
    });
  }

  async deleteSubscriptionPlan(planId: string) {
    return await this.sendRequest({
      method: HTTP_METHODS.DELETE,
      url: this.getUrl(`/${planId}`),
    });
  }
}
