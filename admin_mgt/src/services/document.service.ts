import { DOCUMENT_BASE_URL, HTTP_METHODS } from '../constants/values.constants';
import { GetDocumentStatsResponse } from '../interfaces/document.interfaces';
import { DocumentFilter, TimeFrameFilterType } from '../types';
import BaseServices from './base.service';

export default class DocumentServices extends BaseServices {
  constructor() {
    super(`${DOCUMENT_BASE_URL}/documents`);
  }

  async getAllOrganizationDocuments(orgId: string, page = 1, limit = 50) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl(),
      params: { organizationId: orgId, page, limit },
    });
  }

  async getFilteredOrganizationDocuments(
    orgId: string,
    filter: DocumentFilter,
    page = 1,
    limit = 50
  ) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl('/filter'),
      params: { organizationId: orgId, type: filter.type.toLowerCase(), page, limit },
    });
  }

  async getAllArchivedOrganizationDocuments(orgId: string, page = 1, limit = 50) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl('/archived'),
      params: { organizationId: orgId, page, limit },
    });
  }

  async getDocumentStats(period: TimeFrameFilterType) {
    return await this.sendRequest<GetDocumentStatsResponse>({
      method: HTTP_METHODS.GET,
      url: this.getUrl('/stats'),
      params: { period },
    });
  }
}
