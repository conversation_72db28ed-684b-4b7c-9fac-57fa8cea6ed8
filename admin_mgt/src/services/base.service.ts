import { getAdminRequestOptions } from '../api/helpers';
import axiosInstance from '../config/axios';
import { ErrorWrapper } from '../helpers/class.helpers';
import { RequestOptions } from '../types';

export default class BaseServices extends ErrorWrapper {
  constructor(private baserUrl: string) {
    super();
  }

  protected getUrl(suffix: string = '') {
    return `${this.baserUrl}${suffix}`;
  }

  protected async sendRequest<T>(options: RequestOptions) {
    const axiosOptions = getAdminRequestOptions({
      ...options,
      authHeader: true,
    });
    const { status, data } = await axiosInstance.request<T>(axiosOptions);
    return { status, data };
  }
}
