import { HTTP_METHODS, AUTH_AND_ACCOUNT_BASE_URL } from '../constants/values.constants';
import { GetManyUserResponse, GetUserStatsResponse } from '../interfaces/user.interface';
import { TimeFrameFilterType } from '../types';
import BaseServices from './base.service';

export default class UserServices extends BaseServices {
  constructor() {
    super(`${AUTH_AND_ACCOUNT_BASE_URL}/users`);
  }

  async createUser(payload: Record<string, any>) {
    return await this.sendRequest({ method: HTTP_METHODS.POST, url: this.getUrl(), payload });
  }

  async getAllUsers(offset = 1, limit = 50) {
    return await this.sendRequest<GetManyUserResponse>({
      method: HTTP_METHODS.GET,
      url: this.getUrl(),
      params: { offset, limit },
    });
  }

  async getOneUser(userId: string) {
    return await this.sendRequest({ method: HTTP_METHODS.GET, url: this.getUrl(`/${userId}`) });
  }

  async getUserStats(filter: TimeFrameFilterType) {
    return await this.sendRequest<GetUserStatsResponse>({
      method: HTTP_METHODS.GET,
      url: this.getUrl('/overview'),
      params: { period: filter },
    });
  }
}
