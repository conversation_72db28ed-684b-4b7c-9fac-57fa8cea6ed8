import { HTTP_METHODS, AUTH_AND_ACCOUNT_BASE_URL } from '../constants/values.constants';
import {
  GetAllUserSubscriptionResponse,
  GetUserSubscriptionStatsResponse,
} from '../interfaces/user-subscriptions.interface';
import { TIME_FRAME_FILTER } from '../models/enums';
import { TimeFrameFilterType } from '../types';
import BaseServices from './base.service';

export default class UserSubscriptionServices extends BaseServices {
  constructor() {
    super(`${AUTH_AND_ACCOUNT_BASE_URL}/subscriptions`);
  }

  async getUserSubscriptionStats(filter: TimeFrameFilterType = TIME_FRAME_FILTER.MONTHLY) {
    return await this.sendRequest<GetUserSubscriptionStatsResponse>({
      method: HTTP_METHODS.GET,
      url: this.getUrl('/overview'),
      params: { period: filter },
    });
  }

  async getUserSubscriptions(startDate: string, endDate: string, offset = 1, limit = 50) {
    return await this.sendRequest<GetAllUserSubscriptionResponse>({
      method: HTTP_METHODS.GET,
      url: this.getUrl(),
      params: {
        offset,
        limit,
        // startDate,
        // endDate,
      },
    });
  }
}
