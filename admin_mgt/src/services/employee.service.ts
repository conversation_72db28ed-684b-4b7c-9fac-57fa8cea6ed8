import { HTTP_METHODS, EMPLOYEE_BASE_URL } from '../constants/values.constants';
import BaseServices from './base.service';

export default class EmployeeServices extends BaseServices {
  constructor() {
    super(`${EMPLOYEE_BASE_URL}/employees`);
  }

  async getAllOrganizationEmployee(orgId: string, page: number, limit: number) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl(),
      params: { organizationId: orgId, page, limit },
    });
  }

  async getOneOrganizationEmployee(orgId: string, employeeId: string) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl(`/${employeeId}`),
      params: { organizationId: orgId },
    });
  }

  async getAllLeavesForOrganizationEmployee(
    orgId: string,
    employeeId: string,
    page: number,
    limit: number
  ) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl(`/${employeeId}/leaves`),
      params: { organizationId: orgId, page, limit },
    });
  }

  async getOneLeaveForOrganizationEmployee(orgId: string, employeeId: string, leaveId: string) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl(`/${employeeId}/leaves/${leaveId}`),
      params: { organizationId: orgId },
    });
  }

  async getAllPaymentHistoryForOrganizationEmployee(
    orgId: string,
    employeeId: string,
    page: number,
    limit: number
  ) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl(`/${employeeId}/payments`),
      params: { organizationId: orgId, page, limit },
    });
  }

  async getOnePaymentHistoryForOrganizationEmployee(
    orgId: string,
    employeeId: string,
    paymentHistoryId: string
  ) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl(`/${employeeId}/payments/${paymentHistoryId}`),
      params: { organizationId: orgId },
    });
  }
}
