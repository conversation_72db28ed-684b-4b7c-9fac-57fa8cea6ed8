import {
  CreateBackupPasswordPayload,
  EnableGoogleAuthenticatorPayload,
  LoginPayload,
  RefreshAuthTokenPayload,
  ResendOtpPayload,
  Verify2faOtpPayload,
  VerifyBackupPasswordPayload,
  VerifyOtpPayload,
} from '../interfaces/auth.interfaces';
import { HTTP_METHODS, AUTH_AND_ACCOUNT_BASE_URL } from '../constants/values.constants';
import BaseServices from './base.service';

export default class AuthServices extends BaseServices {
  constructor() {
    super(`${AUTH_AND_ACCOUNT_BASE_URL}/auth`);
  }

  async login(payload: LoginPayload) {
    return await this.sendRequest({
      method: HTTP_METHODS.POST,
      url: this.getUrl('/login'),
      payload,
    });
  }

  async verifyBackupPassword(payload: VerifyBackupPasswordPayload) {
    const { email, backupPassword, action } = payload;

    return await this.sendRequest({
      method: HTTP_METHODS.POST,
      url: this.getUrl('/2fa/backup/verify'),
      params: { action },
      payload: { email, backupPassword },
    });
  }

  async verifyEmailOtp(payload: VerifyOtpPayload) {
    const { email, otp } = payload;

    return await this.sendRequest({
      method: HTTP_METHODS.POST,
      url: this.getUrl('/verify-otp'),
      payload: { email, otp },
    });
  }

  async verify2faOtp(payload: Verify2faOtpPayload) {
    const { action, email, otp } = payload;

    return await this.sendRequest({
      method: HTTP_METHODS.POST,
      url: this.getUrl('/2fa/verify'),
      params: { action },
      payload: { email, otp },
    });
  }

  async resendOtp(payload: ResendOtpPayload) {
    return await this.sendRequest({
      method: HTTP_METHODS.POST,
      url: this.getUrl('/resend-otp'),
      payload,
    });
  }

  async refreshAuthToken(payload: RefreshAuthTokenPayload) {
    return await this.sendRequest({
      method: HTTP_METHODS.POST,
      url: this.getUrl('/refresh-token'),
      payload,
    });
  }

  async viewBackupPasswordHint(email: string) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl('/2fa/backup/view'),
      params: { email },
    });
  }

  async getGoogleAuthenticatorSecretKey(email: string) {
    return await this.sendRequest({
      method: HTTP_METHODS.GET,
      url: this.getUrl('/2fa/credentials'),
      params: { email },
    });
  }

  async enableGoogleAuthenticator(payload: EnableGoogleAuthenticatorPayload) {
    const { email, secretKey } = payload;

    return await this.sendRequest({
      method: HTTP_METHODS.PATCH,
      url: this.getUrl('/2fa/enable'),
      params: { email },
      payload: { secretKey },
    });
  }

  async createBackupPassword(payload: CreateBackupPasswordPayload) {
    const { email, backupPassword, backupPasswordHint } = payload;

    return await this.sendRequest({
      method: HTTP_METHODS.POST,
      url: this.getUrl('/2fa/backup/create'),
      params: { email },
      payload: { backupPassword, backupPasswordHint },
    });
  }
}
