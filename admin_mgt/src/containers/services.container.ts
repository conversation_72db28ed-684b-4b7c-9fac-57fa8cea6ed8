import DocumentServices from '../services/document.service';
import EmployeeServices from '../services/employee.service';
import OrganizationServices from '../services/organization.service';
import SubscriptionPlanServices from '../services/subscription-plan.service';
import Container from './container.global';
import UserSubscriptionServices from '../services/user-subscription.service';
import UserServices from '../services/users.service';
import DashboardServices from '../services/dashboard.service';
import AuthServices from '../services/auth.service';

const services = new Container('services');

services.register('authServices', new AuthServices());

services.register('employeeService', new EmployeeServices());

services.register('documentServices', new DocumentServices());

services.register('organizationServices', new OrganizationServices());

services.register('subscriptionPlanServices', new SubscriptionPlanServices());

services.register('userSubscriptionServices', new UserSubscriptionServices());

services.register('userServices', new UserServices());

services.register(
  'dashboardServices',
  new DashboardServices(
    services.resolve('userServices'),
    services.resolve('documentServices'),
    services.resolve('userSubscriptionServices')
  )
);

export default services;
