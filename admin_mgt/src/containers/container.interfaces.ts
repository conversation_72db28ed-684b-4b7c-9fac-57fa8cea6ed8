import UtilityAPIs from '../api/endpoints/utilities.api';
import EmployeeControllers from '../controllers/employee.controller';
import OrganizationControllers from '../controllers/organization.controller';
import UtilityController from '../controllers/utilities.controller';
import AuthMiddlewares from '../middlewares/auth/auth.middleware';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import DocumentServices from '../services/document.service';
import EmployeeServices from '../services/employee.service';
import SubscriptionPlanServices from '../services/subscription-plan.service';
import UserSubscriptionServices from '../services/user-subscription.service';
import SubscriptionPlanControllers from '../controllers/subscription-plan.controller';
import UserSubscriptionControllers from '../controllers/user-subscription.controller';
import OrganizationServices from '../services/organization.service';
import DocumentControllers from '../controllers/documents.controller';
import UserControllers from '../controllers/users.controller';
import UserServices from '../services/users.service';
import DashboardServices from '../services/dashboard.service';
import DashboardControllers from '../controllers/dashboard.controller';
import AuthControllers from '../controllers/auth.controller';
import AuthServices from '../services/auth.service';

// add all api instances typing
export interface ApiInstances {
  utilityApis: UtilityAPIs;
}

//add all services instances typing
export interface ServiceInstances {
  organizationServices: OrganizationServices;
  employeeService: EmployeeServices;
  documentServices: DocumentServices;
  subscriptionPlanServices: SubscriptionPlanServices;
  userSubscriptionServices: UserSubscriptionServices;
  userServices: UserServices;
  dashboardServices: DashboardServices;
  authServices: AuthServices;
}

//add all controllers instances typing
export interface ControllerInstances {
  utilityControllers: UtilityController;
  employeeControllers: EmployeeControllers;
  documentControllers: DocumentControllers;
  organizationControllers: OrganizationControllers;
  subscriptionPlanControllers: SubscriptionPlanControllers;
  userSubscriptionControllers: UserSubscriptionControllers;
  userControllers: UserControllers;
  dashboardControllers: DashboardControllers;
  authControllers: AuthControllers;
}

//add all middleware instances typing
export interface MiddlewareInstances {
  utilityMiddlewares: UtilityMiddlewares;
  authMiddlewares: AuthMiddlewares;
}

export interface ContainerInstances {
  controllers: ControllerInstances;
  services: ServiceInstances;
  middlewares: MiddlewareInstances;
  apis: ApiInstances;
}
