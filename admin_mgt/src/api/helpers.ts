import { AxiosHeaders, AxiosRequestConfig } from 'axios';
import httpContext from 'express-http-context';
import { HTTP_METHODS } from '../constants/values.constants';
import { RequestOptions } from '../types';
import axiosInstance from '../config/axios';

export const getAdminRequestOptions = (options: RequestOptions) => {
  const method = options.method;
  const url = options.url;

  // overriding auth header to always be true
  options.authHeader = true;
  const headers = AxiosHeaders.from({});

  if (options.authHeader) headers.Authorization = httpContext.get('authHeader');
  if (options.reqId) headers['x-request-id'] = httpContext.get('reqId') || '';
  if (options.accessKey) headers['accesskey'] = options.accessKey;
  if (options.apiKey) headers['x-api-key'] = options.apiKey;
  if (method === HTTP_METHODS.POST || method === HTTP_METHODS.PUT || method === HTTP_METHODS.PATCH)
    headers['Content-Type'] = 'application/json';

  const config = {
    method,
    url,
    headers,
    data: options?.payload,
    params: options?.params,
    responseType: options?.responseType || 'json',
  } as AxiosRequestConfig;

  return config;
};

export async function sendAdminRequest<T>(options: RequestOptions) {
  const { method, url, params } = options;

  const axiosOptions = getAdminRequestOptions({
    method,
    url,
    authHeader: true,
    reqId: true,
    params,
  });

  const { status, data } = await axiosInstance.request<T>(axiosOptions);
  return { status, data };
}
