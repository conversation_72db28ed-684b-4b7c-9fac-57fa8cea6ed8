import axiosInstance from '../../config/axios';
import { HTTP_METHODS } from '../../constants/values.constants';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { LogDetails } from '../../interfaces/log.interfaces';
import { getAdminRequestOptions } from '../helpers';
import { UTILITIES_API_URLS } from '../urls';

export interface LogResponse {
  status: string;
  message: string;
  data: {
    id: number;
    serverDetails: object;
    requestDetails: object;
    responseDetails: object;
    userDetails: object;
    createdAt: Date;
    updatedAt: Date;
  };
}
export default class UtilityAPIs extends Error<PERSON>rapper {
  async sendLog(payload: LogDetails) {
    const method = HTTP_METHODS.POST;
    const url = UTILITIES_API_URLS.sendLog;
    const options = getAdminRequestOptions({ method, url, payload });
    return (await axiosInstance.request<LogResponse>(options)).data as LogResponse;
  }
}
