import Joi from 'joi';
import {
  DOCUMENT_TYPES_ARRAY,
  EMPLOYMENT_STATUS_ARRAY,
  TIME_FRAME_FILTER_ARRAY,
} from '../../../models/enums';
import { DAY_WEEK_MONTH_YEAR_FILTER_ARRAY } from '../../../constants/values.constants';
import { DocumentFilter, TimeFrameFilterType, UserSubscriptionsFilter } from '../../../types';
import { getDateSchema, getEnumSchema } from './global.schema';

export const employmentStatusSchema = Joi.object<{ employmentStatus: string }>({
  employmentStatus: Joi.string()
    .valid(...EMPLOYMENT_STATUS_ARRAY)
    .required()
    .messages({
      'string.base': 'employment status must be a string',
      'string.empty': 'employment status cannot be empty',
      'any.required': 'employment status is required as a query parameter',
      'any.only': `employment status can only be ${EMPLOYMENT_STATUS_ARRAY.join(' or ')}`,
    }),
});

export const dayWeekMonthYearSchema = Joi.object<{ filter: string }>({
  filter: Joi.string()
    .lowercase()
    .allow(...DAY_WEEK_MONTH_YEAR_FILTER_ARRAY)
    .required()
    .messages({
      'string.base': 'filter must be a string',
      'string.empty': 'filter cannot be empty',
      'any.required': 'filter is required as a query parameter',
      'any.only': `allowed filter values are either: ${DAY_WEEK_MONTH_YEAR_FILTER_ARRAY.join(' or ')}`,
    }),
});

export const documentFilterSchema = Joi.object<DocumentFilter>({
  type: Joi.string()
    .lowercase()
    .valid(...DOCUMENT_TYPES_ARRAY)
    .required()
    .messages({
      'string.base': 'document type must be a string',
      'string.empty': 'document type cannot be empty',
      'any.required': 'document type is required as a query parameter',
      'any.only': `allowed document types are either: ${DOCUMENT_TYPES_ARRAY.join(' or ')}`,
    }),
});

export const userSubscriptionFilterSchema = Joi.object<UserSubscriptionsFilter>({
  startDate: Joi.date().iso().required().messages({
    'date.base': 'start date must be a valid date.',
    'date.format': 'start date must be in iso 8601 format.',
    'any.required': 'start date is required.',
  }),

  endDate: getDateSchema({ name: 'end date', defaultValue: new Date().toISOString() })
    .greater(Joi.ref('startDate'))
    .messages({ 'date.greater': 'end date must be greater than start date' }),
});

export const timeFrameFilterSchema = Joi.object<{ period: TimeFrameFilterType }>({
  period: Joi.string()
    .lowercase()
    .valid(...TIME_FRAME_FILTER_ARRAY)
    .required()
    .messages({
      'any.required': 'period is required as a query parameter',
      'string.base': 'period must be a string',
      'any.only': `period must be one of: ${TIME_FRAME_FILTER_ARRAY.join(', ')}`,
    }),
});

export const _2faVerificationActionQuerySchema = Joi.object({
  action: getEnumSchema({ name: 'action', validValues: ['login'], isRequired: true }),
});
