import Joi from 'joi';
import { SubscriptionPlanPayload } from '../../../../interfaces/subscription-plan.interfaces';

export const subscriptionSchema = Joi.object<SubscriptionPlanPayload>({
  name: Joi.string().min(3).max(100).required().messages({
    'string.base': 'Subscription name must be a string',
    'string.empty': 'Subscription name is required',
    'string.min': 'Subscription name must be at least 3 characters',
    'string.max': 'Subscription name must not exceed 100 characters',
  }),

  description: Joi.string().min(10).required().messages({
    'string.base': 'Description must be a string',
    'string.empty': 'Description is required',
    'string.min': 'Description must be at least 10 characters long',
  }),

  features: Joi.object({
    authorized: Joi.array().items(Joi.string().min(3).required()).min(1).required().messages({
      'array.base': 'Authorized features must be an array',
      'array.min': 'At least one authorized feature is required',
      'string.base': 'Authorized feature must be a string',
      'string.empty': 'Authorized feature cannot be empty',
    }),

    restricted: Joi.array().items(Joi.string().min(3).required()).min(1).required().messages({
      'array.base': 'Restricted features must be an array',
      'array.min': 'At least one restricted feature is required',
      'string.base': 'Restricted feature must be a string',
      'string.empty': 'Restricted feature cannot be empty',
    }),
  })
    .required()
    .messages({
      'object.base': 'Features must be a valid object',
      'any.required': 'Features field is required',
    }),

  pricing: Joi.array()
    .items(
      Joi.object({
        country: Joi.string().length(2).uppercase().required().messages({
          'string.length': 'Country code must be exactly 2 characters',
          'string.uppercase': 'Country code must be in uppercase (e.g., NG, US)',
          'string.empty': 'Country code is required',
        }),

        currency: Joi.string().length(3).uppercase().required().messages({
          'string.length': 'Currency code must be exactly 3 characters',
          'string.uppercase': 'Currency code must be in uppercase (e.g., NGN, USD)',
          'string.empty': 'Currency code is required',
        }),

        price: Joi.number().min(0).required().messages({
          'number.base': 'Price must be a number',
          'number.min': 'Price must be greater than or equal to 0',
          'any.required': 'Price is required',
        }),

        billing_cycle: Joi.string().valid('MONTHLY', 'ANNUAL').required().messages({
          'any.only': 'Billing cycle must be either MONTHLY or ANNUAL',
          'string.empty': 'Billing cycle is required',
        }),
      })
    )
    .min(1)
    .required()
    .messages({
      'array.base': 'Pricing must be an array',
      'array.min': 'At least one pricing option is required',
    }),
});

export const subscriptionArraySchema = Joi.object<{ subscriptions: SubscriptionPlanPayload[] }>({
  subscriptions: Joi.array().items(subscriptionSchema).min(1).required().messages({
    'array.base': 'Subscriptions must be an array of subscription objects',
    'array.min': 'At least one subscription must be provided',
    'any.required': 'Array of subscriptions is required',
  }),
});
