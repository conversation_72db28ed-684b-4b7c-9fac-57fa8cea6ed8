import Joi from 'joi';
import {
  CreateBackupPasswordPayload,
  EnableGoogleAuthenticatorPayload,
  LoginPayload,
  RefreshAuthTokenPayload,
  ResendOtpPayload,
  Verify2faOtpPayload,
  VerifyBackupPasswordPayload,
  VerifyOtpPayload,
} from '../../../../interfaces/auth.interfaces';
import { getEmailSchema, getEnumSchema, getNumberSchema, getStringSchema } from '../global.schema';

export const loginSchema = Joi.object<LoginPayload>({
  email: getEmailSchema(),
  password: getStringSchema({ name: 'password', isRequired: true }),
})
  .required()
  .messages({
    'object.base': 'request body must be an object',
    'any.required': 'request body is required',
  });

export const verifyOtpSchema = Joi.object<VerifyOtpPayload>({
  email: getEmailSchema(),
  otp: getNumberSchema({ name: 'otp', isRequired: true }),
})
  .required()
  .messages({
    'object.base': 'request body must be an object',
    'any.required': 'request body is required',
  });

export const verify2faOtpSchema = Joi.object<Verify2faOtpPayload>({
  email: getEmailSchema(),
  action: getEnumSchema({ name: 'action', validValues: ['login'], isRequired: true }),
  otp: getNumberSchema({ name: 'otp', isRequired: true }),
})
  .required()
  .messages({
    'object.base': 'request body must be an object',
    'any.required': 'request body is required',
  });

export const resendOtpSchema = Joi.object<ResendOtpPayload>({
  email: getEmailSchema(),
})
  .required()
  .messages({
    'object.base': 'request body must be an object',
    'any.required': 'request body is required',
  });

export const refreshAuthTokenSchema = Joi.object<RefreshAuthTokenPayload>({
  refreshToken: getStringSchema({ name: 'refresh token', isRequired: true }),
})
  .required()
  .messages({
    'object.base': 'request body must be an object',
    'any.required': 'request body is required',
  });

export const verifyBackupPasswordSchema = Joi.object<VerifyBackupPasswordPayload>({
  email: getEmailSchema(),
  backupPassword: getStringSchema({ name: 'backup password', isRequired: true }),
  action: getEnumSchema({ name: 'action', validValues: ['login'], isRequired: true }),
})
  .required()
  .messages({
    'object.base': 'request body must be an object',
    'any.required': 'request body is required',
  });

export const enableGoogleAuthenticatorSchema = Joi.object<EnableGoogleAuthenticatorPayload>({
  email: getEmailSchema(),
  secretKey: getStringSchema({ name: 'secret key', isRequired: true }),
})
  .required()
  .messages({
    'object.base': 'request body must be an object',
    'any.required': 'request body is required',
  });

export const createBackupPasswordSchema = Joi.object<CreateBackupPasswordPayload>({
  email: getEmailSchema(),
  backupPassword: getStringSchema({ name: 'backup password', isRequired: true }),
  backupPasswordHint: getStringSchema({ name: 'backup password hint', isRequired: true }),
})
  .required()
  .messages({
    'object.base': 'request body must be an object',
    'any.required': 'request body is required',
  });
