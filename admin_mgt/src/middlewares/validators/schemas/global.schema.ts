import Joi from 'joi';

export function getEmailSchema(isRequired: boolean = true) {
  const schema = Joi.string().email({ minDomainSegments: 2 });

  if (isRequired) schema.required();

  schema.messages({
    'any.required': 'email is required',
    'string.base': 'email must be a string',
    'string.empty': 'email cannot be empty',
    'string.email': 'email must be a valid email address',
  });

  return schema;
}

interface StringSchemaOptions {
  name: string;
  min?: number;
  max?: number;
  isRequired?: boolean;
  defaultValue?: string;
}

export function getStringSchema(options: StringSchemaOptions) {
  const { name, min = 3, max = 20, isRequired = false, defaultValue = undefined } = options;

  let schema = Joi.string().min(min).max(max);

  if (isRequired) schema = schema.required();
  else schema = schema.allow('');

  if (defaultValue !== undefined) schema = schema.default(defaultValue);

  schema.messages({
    'any.required': `${name} is required`,
    'string.base': `${name} must be a string`,
    'string.empty': `${name} cannot be empty`,
    'string.min': `${name} must be at least ${min} characters long`,
    'string.max': `${name} must be at most ${max} characters long.`,
  });

  return schema;
}

interface NumberSchemaOptions {
  name: string;
  isRequired?: boolean;
}

export function getNumberSchema(options: NumberSchemaOptions) {
  const { name, isRequired = false } = options;
  const schema = Joi.number();

  if (isRequired) schema.required();

  schema.messages({
    'any.required': `${name} is required`,
    'number.base': `${name} must be a number`,
  });

  return schema;
}

interface EnumSchemaOptions {
  isRequired?: boolean;
  defaultValue?: string;
  validValues: string[];
  name: string;
}

export function getEnumSchema(options: EnumSchemaOptions) {
  const { isRequired = false, validValues, defaultValue = undefined, name = 'value' } = options;

  let schema = Joi.string().valid(...validValues);

  if (isRequired) schema = schema.required();

  if (defaultValue !== undefined) schema = schema.default(defaultValue);

  schema = schema.messages({
    'any.required': `${name} is required`,
    'string.base': `${name} must be a string`,
    'any.only': `${name} must be one of [${validValues.join(', ')}]`,
  });

  return schema;
}

interface DateSchemaOptions {
  name: string;
  isRequired?: boolean;
  defaultValue?: string | Date;
}

export function getDateSchema(options: DateSchemaOptions) {
  const { isRequired = false, defaultValue = undefined, name } = options;

  let schema = Joi.date().iso().allow('');

  if (isRequired) schema = schema.required();
  else schema = schema.allow('');

  if (defaultValue !== undefined) schema = schema.default(defaultValue);

  schema = schema.messages({
    'any.required': `${name} is required`,
    'date.base': `${name} must be a valid date`,
    'date.format': `${name} must be in ISO format (YYYY-MM-DD)`,
    'date.empty': `${name} cannot be empty`,
  });

  return schema;
}
