import { NextFunction, Request, Response } from 'express';
import { catchAsync } from '../../utilities/catch-async-error';
import { Schema } from 'joi';
import { BadRequestError } from '../../helpers/error.helpers';
import { getJoiValidationErrorMessage } from './helpers.validators';
import { validate as isValidUUID } from 'uuid';
import { organizationIdSchema } from './schemas/route.params.schema';
import { RouteParams } from '../../constants/route-params.constants';

export function validateRequestBody(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.body, { abortEarly: false, stripUnknown: true });
    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.body = value;
    next();
  });
}

export function validateQueryParams(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.query, { stripUnknown: true, abortEarly: true });

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    next();
  });
}

export function validateOrganizationId() {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const { value, error } = organizationIdSchema.validate(req.params.organizationId, {
      stripUnknown: true,
      abortEarly: true,
    });

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.params.organizationId = value;
    next();
  });
}

export function validateRouteIdParameter(idName: (typeof RouteParams)[keyof typeof RouteParams]) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const routeId = req.params[`${idName}`]?.trim();

    if (!routeId) throw new BadRequestError(`${idName} is required.`);

    if (!isValidUUID(routeId)) throw new BadRequestError(`${idName} must be a valid uuid.`);
    next();
  });
}

export function validateRouteIdParams(idNames: (typeof RouteParams)[keyof typeof RouteParams][]) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    idNames.forEach((idName) => {
      const routeId = req.params[`${idName}`]?.trim();

      if (!routeId) throw new BadRequestError(`${idName} is required.`);

      if (!isValidUUID(routeId)) throw new BadRequestError(`${idName} must be a valid uuid.`);
    });

    next();
  });
}
