import { NextFunction, Request, Response } from 'express';
import geoip from 'geoip-lite';
import {
  getPublicAddress,
  getUserAgentHeader,
  getUserTimeZone,
} from '../../utilities/global.utilities';
import { format, toZonedTime } from 'date-fns-tz';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      userTimeZone: string;
    }
  }
}

export interface DeviceDetails {
  ip: string;
  userAgent: string;
  browser: string;
  os: string;
  timezone: string;
  time: string;
  location: string;
}

export default class UtilityMiddlewares extends RequestHandlerErrorWrapper {
  private captureDeviceDetails(req: Request, res: Response) {
    const ip = getPublicAddress(req) || 'Unknown';
    const userAgent = getUserAgentHeader(req);
    const browser =
      RegExp(/(Firefox|Chrome|Safari|Opera|MSIE|Trident)/i).exec(userAgent)?.[0] || 'Unknown';
    const os = RegExp(/\(([^)]+)\)/).exec(userAgent)?.[1] || 'Unknown';

    const geo = geoip.lookup(ip);
    const location: string = geo?.country || 'US';
    const timezone = geo?.timezone || 'UTC';
    const time = format(toZonedTime(new Date(), timezone), 'yyyy-MM-dd HH:mm:ssXXX');

    const deviceDetails: DeviceDetails = {
      ip,
      userAgent,
      browser,
      os,
      timezone,
      time,
      location,
    };
    return (res.locals = { ...res.locals, deviceDetails });
  }

  async captureAppDetails(req: Request, res: Response, next: NextFunction) {
    req.userTimeZone = getUserTimeZone(req);
    this.captureDeviceDetails(req, res);
    next();
  }
}
