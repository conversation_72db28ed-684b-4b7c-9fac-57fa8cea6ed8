import * as Sentry from '@sentry/node';
import logger from '../../utilities/logger';
import { Request, Response } from 'express';
import { errorResponse } from '../../helpers/response.helpers';
import { AppError, InternalServerError } from '../../helpers/error.helpers';
import { NextFunction } from '@sentry/node/build/types/integrations/tracing/nest/types';

function handleInternalServerError(): AppError {
  return new InternalServerError();
}

function handleError(err: Error): AppError {
  if (err instanceof AppError) {
    return err;
  } else {
    return handleInternalServerError();
  }
}

function logError(req: Request, err: Error): void {
  let errDetails: Record<string, any>;

  if (err instanceof AppError) {
    errDetails = {
      url: req.originalUrl,
      method: req.method,
      body: req.body,
      ip: req.ip,
      errorName: err.name,
      message: err.message,
      statusCode: err?.statusCode,
      status: err?.status,
      errorLocation: err?.location,
      stack: err.stack,
    };
  } else {
    errDetails = {
      url: req.originalUrl,
      method: req.method,
      body: req.body,
      ip: req.ip,
      errorName: err.name,
      message: err.message,
      error: err,
      stack: err.stack,
    };
  }

  logger.error('ADMIN APP ERROR', errDetails);
}

export default function globalErrorHandler(
  err: Error,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction
) {
  Sentry.captureException(err.stack);
  logError(req, err);
  const appError = handleError(err);
  return errorResponse(res, appError);
}
