import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { NotAuthenticatedError } from '../../helpers/error.helpers';
import { Request, Response, NextFunction } from 'express';
import httpContext from 'express-http-context';

export default class AuthMiddlewares extends RequestHandlerErrorWrapper {
  async checkAuthTokenAndSetAuthHeader(req: Request, _res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];
    if (!authHeader) throw new NotAuthenticatedError();
    httpContext.set('authHeader', authHeader);
    next();
  }
}
