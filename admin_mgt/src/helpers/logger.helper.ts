import winston from 'winston';
import { LogContext } from '../interfaces';

export class EnhancedLogger {
  private winston: winston.Logger;

  constructor(winstonInstance: winston.Logger) {
    this.winston = winstonInstance;
  }

  error(message: string, meta?: LogContext | Error): void {
    if (meta instanceof Error) {
      this.winston.error(message, { error: meta.message, stack: meta.stack });
    } else {
      this.winston.error(message, meta);
    }
  }

  warn(message: string, meta?: LogContext): void {
    this.winston.warn(message, meta);
  }

  info(message: string, meta?: LogContext): void {
    this.winston.info(message, meta);
  }

  debug(message: string, meta?: LogContext): void {
    this.winston.debug(message, meta);
  }

  http(
    req: { method: string; url: string; ip?: string },
    res: { statusCode: number },
    duration: number,
    meta?: LogContext
  ): void {
    this.winston.info('HTTP Request', {
      ...meta,
      method: req.method,
      url: req.url,
      ip: req.ip,
      statusCode: res.statusCode,
      duration,
      type: 'http_request',
    });
  }

  db(operation: string, table: string, duration: number, meta?: LogContext): void {
    this.winston.info('Database Operation', {
      ...meta,
      operation,
      table,
      duration,
      type: 'db_operation',
    });
  }

  audit(action: string, entity: string, meta?: LogContext): void {
    this.winston.info('Audit Event', {
      ...meta,
      action,
      entity,
      type: 'audit',
    });
  }

  performance(operation: string, duration: number, meta?: LogContext): void {
    const level = duration > 1000 ? 'warn' : 'info';
    this.winston[level]('Performance Metric', {
      ...meta,
      operation,
      duration,
      type: 'performance',
    });
  }

  security(event: string, meta?: LogContext): void {
    this.winston.warn('Security Event', {
      ...meta,
      event,
      type: 'security',
    });
  }

  child(defaultMeta: LogContext): EnhancedLogger {
    const childLogger = this.winston.child(defaultMeta);
    return new EnhancedLogger(childLogger);
  }

  getWinston(): winston.Logger {
    return this.winston;
  }
}
