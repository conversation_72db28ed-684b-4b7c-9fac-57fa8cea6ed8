import { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import fs from 'fs';
import logger from '../utilities/logger';
import * as Sentry from '@sentry/node';
import { ERRORS } from '../constants/errors.constants';
import { LogDetails, RequestLogDetails } from '../interfaces/log.interfaces';
import { AppError, getErrorCode } from './error.helpers';
import apisContainer from '../containers/apis.container';
import { MSResponse } from '../interfaces/global.interfaces';

type FileResponseOptions =
  | { filename: string; buffer: Buffer; filepath?: never }
  | { filename: string; filepath: string; buffer?: never };

const sendLog = async (
  res: Response,
  action: string,
  statusCode: number,
  message: string,
  data?: any
) => {
  const requestLogDetails = res.locals.requestLogDetails as RequestLogDetails;

  const logDetails: LogDetails = {
    anonymous: requestLogDetails.userDetails?.id ? false : true,
    userId: requestLogDetails?.userDetails?.id,
    orgId: requestLogDetails?.userDetails?.orgId,
    action,
    details: {
      ...requestLogDetails,
      responseDetails: {
        statusCode,
        message,
        data,
      },
    },
  };

  apisContainer
    .resolve('utilityApis')
    .sendLog(logDetails)
    .catch((error) => {
      Sentry.captureException(error);
    });
};

export function successResponse(
  res: Response,
  resMsgAndCode: [string, StatusCodes],
  action: string,
  data?: any,
  meta?: any
) {
  process.nextTick(() => {
    sendLog(res, action, resMsgAndCode[1], resMsgAndCode[0], data).catch((error) => {
      Sentry.captureException(error);
    });
  });

  if (resMsgAndCode[1] === StatusCodes.NO_CONTENT) return res.status(resMsgAndCode[1]).end();

  return res
    .status(resMsgAndCode[1])
    .json({
      status: 'success',
      message: resMsgAndCode[0],
      data,
      code: `S${resMsgAndCode[1]}`,
      meta,
    })
    .end();
}

export function sendFileResponse(res: Response, action: string, options: FileResponseOptions) {
  process.nextTick(() => {
    sendLog(res, action, 200, `${options.filename} was potentially downloaded successfully`).catch(
      (error) => {
        Sentry.captureException(error);
      }
    );
  });

  // sending file saved in system RAM as buffer
  if (options.filename && options.buffer) {
    res.setHeader('Content-Disposition', `attachment; filename="${options.filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', options.buffer.length);

    res.end(options.buffer);
  }

  // sending files saved on system ROM
  else {
    const stream = fs.createReadStream(options.filepath);

    res.setHeader('Content-Disposition', `attachment; filename="${options.filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    stream.pipe(res);

    stream.on('error', (error) => {
      Sentry.captureException(error);

      logger.error('ADMIN STREAM ERROR', {
        name: 'Stream Error:',
        location: 'sending file as stream for download',
        error,
      });

      fs.unlink(options.filepath, (error) => {
        if (error) {
          Sentry.captureException(error);
          logger.error('ADMIN FILE DELETION ERROR', {
            name: 'FileDeletionError',
            location: 'deleting file from drive after downloading',
            error,
          });
        }
      });

      return res
        .status(500)
        .json({
          status: 'error',
          message: ERRORS.fileDownloadingError[0],
          code: getErrorCode(500),
        })
        .end();
    });

    stream.on('end', () => {
      fs.unlink(options.filepath, (error) => {
        if (error) {
          Sentry.captureException(error);
          logger.error('ADMIN FILE DELETION ERROR', {
            name: 'FileDeletionError',
            location: 'deleting file from drive after downloading',
            error,
          });
        }
      });
    });
  }
}

export function errorResponse(res: Response, error: AppError) {
  return res
    .status(error.statusCode)
    .json({ status: error.status, message: error.message, code: getErrorCode(error.statusCode) })
    .end();
}

export function sendAdminResponse(
  res: Response,
  statusCode: number,
  data: MSResponse<any> | Record<any, any>
) {
  return res
    .status(statusCode)
    .json({ ...data })
    .end();
}
