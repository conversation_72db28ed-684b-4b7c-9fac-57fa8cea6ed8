import dotenv from 'dotenv';
if (process.env.NODE_ENV === 'production') {
  dotenv.config();
} else if (process.env.NODE_ENV === 'development') {
  dotenv.config({ path: '.env', debug: true });
} else {
  dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true });
}

import app from './app';
import logger from './utilities/logger';
import { Server } from 'http';

const serverName = 'Admin App Management MS. 💻';
const environment = `${process.env.NODE_ENV}`;
const port = process.env.PORT || 7777;

const server: Server = app.listen(port, async () => {
  logger.info('ADMIN SERVER CONNECTED', {
    serverName,
    environment,
    port,
    startTimeStamp: new Date().toISOString(),
  });
});

const closeConnections = async () => {
  return;
};

process.on('unhandledRejection', async (err: Error) => {
  await closeConnections();
  server.close(() => {
    logger.error('UNHANDLED REJECTION IN ADMIN💥 Shutting down...', {
      title: 'UNHANDLED REJECTION 💥 Shutting down...',
      name: err?.name,
      message: err?.message,
      serverName,
      stopTimeStamp: new Date().toISOString(),
      error: err,
    });
    process.exit(1);
  });
});

process.on('uncaughtException', async (err: Error) => {
  await closeConnections();
  server.close(() => {
    logger.error('UNCAUGHT EXCEPTION IN ADMIN💥 Shutting down...', {
      title: 'UNCAUGHT EXCEPTION 💥 Shutting down...',
      name: err?.name,
      message: err?.message,
      serverName,
      stopTimeStamp: new Date().toISOString(),
      error: err,
    });
    process.exit(1);
  });
});
