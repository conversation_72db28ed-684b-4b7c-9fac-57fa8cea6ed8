version: "3.9"

services:
  admin-app:
    image: "digit-tally-backend-admin-app"
    secrets:
      - source: admin-env
        target: /app/.env
        uid: '103'
        gid: '103'
        mode: 0440
    networks:
      - digit-tally-admin-network  
    deploy: &deploy
      mode: replicated
      replicas: 1
      rollback_config:
        parallelism: 2
        delay: 10s
        order: start-first
        failure_action: pause
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
        failure_action: rollback

  admin-balancer:
    image: nginx:latest
    volumes:
      - ../nginx/nginx.production.conf:/etc/nginx/nginx.conf:ro
    ports:
      - 8877:80
    command: [nginx-debug, '-g', 'daemon off;']
    depends_on:
      - admin-app
    networks:
      - digit-tally-admin-network
    deploy: *deploy

networks:
  digit-tally-admin-network:
    external: true 

secrets:
  admin-env:
    external: true
