events {
    worker_connections 768;
}

http {
    include /etc/nginx/mime.types;

    server {
        listen 80 default_server;

        location / {
            try_files $uri @admin-fe;
        }

        location @admin-fe {
            proxy_pass http://admin-frontend:3002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/v1/ {
            proxy_pass http://admin-app:7777;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header 'Access-Control-Expose-Headers' 'x-dgt-2fa-auth, Content-Length' always;
            add_header 'Access-Control-Expose-Headers' 'X-Dgt-2fa-Auth, Content-Length' always;
            add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type, x-dgt-2fa-auth' always;
            add_header Content-Security-Policy "default-src * data: 'unsafe-eval' 'unsafe-inline'" always;
        }

        # Serve static files from the .next directory
        location /_next/ {
            try_files $uri @admin-fe;
            expires 1y;
            access_log off;
        }
    }

    include /etc/nginx/conf.d/*.conf;
}
