{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "outDir": "./dist", "rootDir": "./src", "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitAny": false, "allowJs": true, "typeRoots": ["./node_modules/@types"], "types": ["node"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/interfaces/user-subscriptions.interface.ts"], "exclude": ["node_modules"]}