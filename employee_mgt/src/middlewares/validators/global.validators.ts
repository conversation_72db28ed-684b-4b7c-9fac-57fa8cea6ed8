import { NextFunction, Request, Response } from 'express';
import { catchAsync } from '../../utilities/catch-async-error';
import { Schema, ValidationError } from 'joi';
import { BadRequestError, throwAppError } from '../../helpers/error.helpers';
import { validate as isValidUUID } from 'uuid';
import { StatusCodes } from 'http-status-codes';
import { ROUTE_IDS } from '../../constants/route-params.constant';
import { searchEmployeesSchema } from './schemas/query_params/employees.query.params.schema';
import { ISearchEmployeesQuery } from '../../interfaces/employee.interfaces';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      searchEmployeePayload: ISearchEmployeesQuery;
    }
  }
}

export function getJoiValidationErrorMessage(error: ValidationError) {
  const errorMessage = error.details
    .map((detail) => {
      return detail.message.replace(/"+/g, '');
    })
    .join(', ');

  return errorMessage;
}

export function validateRequestBody(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.body, { stripUnknown: true });

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.body = value;
    next();
  });
}

export function validateQueryParams(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.query, { stripUnknown: true, abortEarly: true });

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    next();
  });
}

export function validateRouteIdParams(name: (typeof ROUTE_IDS)[keyof typeof ROUTE_IDS]) {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const id = req.params[name];

    if (!id) {
      return throwAppError([`${name} is required.`, StatusCodes.BAD_REQUEST]);
    }

    if (typeof id !== 'string' || !isValidUUID(id)) {
      return throwAppError([`${name} must be a valid UUID string.`, StatusCodes.BAD_REQUEST]);
    }

    next();
  });
}

export const validateSearchEmployee = catchAsync(
  async (req: Request, _res: Response, next: NextFunction) => {
    const { value, error } = searchEmployeesSchema.validate(req.query, {
      stripUnknown: true,
      abortEarly: true,
    });

    if (error) throw new BadRequestError(getJoiValidationErrorMessage(error));

    req.searchEmployeePayload = value;

    next();
  }
);
