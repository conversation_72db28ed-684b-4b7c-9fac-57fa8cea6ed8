import Joi from 'joi';
import { IEmployeeAttributes } from '../../../../models/employee.model';
import * as enumValues from '../../../../models/enums';
import { makeSchemaFieldsOptional } from '../helpers.schema';
import { createDeductibleSchema } from './deductible.schema';
import { createPensionSchema } from './pension.schema';
import { IDeductibleAttributes } from '../../../../models/deductible.model';
import { IPensionAttributes } from '../../../../models/pension.model';
import {
  createEmployeeCustomValidator,
  createMultipleEmployeesCustomValidator,
  editEmployeeCustomValidator,
  phoneNumberCustomValidator,
  requireAtLeastOneField,
} from '../../custom-validators.helpers';

const {
  BONUS_INTERVAL_ARRAY,
  BONUS_INTERVAL_ENUM,
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_STATUS_ENUM,
  EMPLOYMENT_TYPE_ARRAY,
  GENDER_ARRAY,
  KIND_OF_PAYMENT_ARRAY,
  MODE_OF_PAYMENT_ARRAY,
} = enumValues;

// create employee schema
export const createEmployeeSchema = Joi.object<IEmployeeAttributes>({
  first_name: Joi.string()
    .trim()
    .lowercase()
    .min(2)
    .max(30)
    .required()
    .pattern(/^[\p{L}][\p{L} '-]*[\p{L}]$/u)
    .messages({
      'any.required': 'first name is required.',
      'string.base': 'first name must be a string.',
      'string.min': 'first name must be at least {#limit} characters long.',
      'string.max': 'first name must not exceed {#limit} characters.',
      'string.pattern.base':
        'first name must contain only letters, spaces, apostrophes, or hyphens.',
    }),

  middle_name: Joi.string()
    .trim()
    .lowercase()
    .min(0)
    .max(30)
    .allow('')
    .default('')
    .optional()
    .pattern(/^[\p{L} '-]*$/u)
    .messages({
      'string.base': 'middle name must be a string.',
      'string.min': 'middle name must be at least {#limit} characters long.',
      'string.max': 'middle name must not exceed {#limit} characters.',
      'string.pattern.base':
        'middle name must contain only letters, spaces, apostrophes, or hyphens.',
    }),

  last_name: Joi.string()
    .trim()
    .lowercase()
    .min(2)
    .max(30)
    .required()
    .pattern(/^[\p{L}][\p{L} '-]*[\p{L}]$/u)
    .messages({
      'any.required': 'last name is required.',
      'string.base': 'last name must be a string.',
      'string.min': 'last name must be at least {#limit} characters long.',
      'string.max': 'last name must not exceed {#limit} characters.',
      'string.pattern.base':
        'last name must contain only letters, spaces, apostrophes, or hyphens.',
    }),

  gender: Joi.string()
    .lowercase()
    .valid(...GENDER_ARRAY)
    .required()
    .messages({
      'any.required': 'gender is required.',
      'string.base': 'gender must be a string.',
      'any.only': `gender must be one of: ${GENDER_ARRAY.join(', ')}.`,
    }),

  date_of_birth: Joi.date().iso().required().messages({
    'any.required': 'date of birth is required.',
    'date.base': 'date of birth must be a valid date.',
    'date.format': 'date of birth must be in iso format (yyyy-mm-dd).',
  }),

  country: Joi.string().trim().lowercase().min(5).max(30).required().messages({
    'any.required': 'country is required.',
    'string.base': 'country must be a string.',
    'string.min': 'country must be at least {#limit} characters long.',
    'string.max': 'country must not exceed {#limit} characters.',
  }),

  home_address: Joi.string().trim().lowercase().min(2).max(200).required().messages({
    'any.required': 'home address is required.',
    'string.base': 'home address must be a string.',
    'string.min': 'home address must be at least {#limit} characters long.',
    'string.max': 'home address must not exceed {#limit} characters.',
  }),

  email: Joi.string().trim().lowercase().email({ minDomainSegments: 2 }).required().messages({
    'any.required': 'email is required.',
    'string.email': 'email must be a valid email address.',
  }),

  phone_number: Joi.string()
    .allow('')
    .pattern(/^\+[1-9]\d{7,14}$/)
    .custom(phoneNumberCustomValidator)
    .required()
    .messages({
      'string.base': 'phone number must be a string',
      'string.pattern.base': 'phone number must be in international format, e.g. +XXXXX...',
      'phone.number.invalid': 'phone number is invalid',
    }),

  emergency_number: Joi.string().trim().min(5).max(15).allow('').messages({
    'string.base': 'emergency number must be a string.',
    'string.min': 'emergency number must be at least {#limit} characters long.',
    'string.max': 'emergency number must not exceed {#limit} characters.',
  }),

  national_id: Joi.string().trim().lowercase().min(5).max(20).allow('').messages({
    'string.base': 'national id must be a string.',
    'string.min': 'national id must be at least {#limit} characters long.',
    'string.max': 'national id must not exceed {#limit} characters.',
  }),

  employment_type: Joi.string()
    .lowercase()
    .valid(...EMPLOYMENT_TYPE_ARRAY)
    .required()
    .messages({
      'any.required': 'employment type is required.',
      'string.base': 'employment type must be a string.',
      'any.only': `employment type must be one of: ${EMPLOYMENT_TYPE_ARRAY.join(', ')}.`,
    }),

  employee_id: Joi.string().trim().lowercase().min(2).max(20).allow('').messages({
    'string.base': 'employee id must be a string.',
    'string.min': 'employee id must be at least {#limit} characters long.',
    'string.max': 'employee id must not exceed {#limit} characters.',
  }),

  employment_start_date: Joi.date().iso().required().messages({
    'any.required': 'employment start date is required.',
    'date.base': 'employment start date must be a valid date.',
    'date.format': 'employment start date must be in iso format (yyyy-mm-dd).',
  }),

  employment_end_date: Joi.date().iso().default(null).allow(null).messages({
    'date.base': 'employment end date must be a valid date.',
    'date.format': 'employment end date must be in iso format (yyyy-mm-dd).',
  }),

  role: Joi.string().trim().lowercase().min(2).max(30).required().messages({
    'any.required': 'role is required.',
    'string.base': 'role must be a string.',
    'string.min': 'role must be at least {#limit} characters long.',
    'string.max': 'role must not exceed {#limit} characters.',
  }),

  kind_of_payment: Joi.string()
    .lowercase()
    .valid(...KIND_OF_PAYMENT_ARRAY)
    .required()
    .messages({
      'any.required': 'kind of payment is required.',
      'string.base': 'kind of payment must be a string.',
      'any.only': `kind of payment must be one of: ${KIND_OF_PAYMENT_ARRAY.join(', ')}.`,
    }),

  mode_of_payment: Joi.string()
    .lowercase()
    .valid(...MODE_OF_PAYMENT_ARRAY)
    .required()
    .messages({
      'any.required': 'mode of payment is required.',
      'string.base': 'mode of payment must be a string.',
      'any.only': `mode of payment must be one of: ${MODE_OF_PAYMENT_ARRAY.join(', ')}.`,
    }),

  salary: Joi.number().precision(2).min(0).default(0).messages({
    'number.base': 'salary must be a number.',
    'number.min': 'salary must be at least 0.01.',
  }),

  hourly_rate: Joi.number().precision(2).min(0).default(0).messages({
    'number.base': 'hourly rate must be a number.',
    'number.min': 'hourly rate must be at least 0.01.',
  }),

  work_hours_per_week: Joi.number().integer().min(1).default(0).messages({
    'number.base': 'work hours per week must be a number.',
    'number.min': 'work hours per week must be at least 1.',
  }),

  bonus_percent: Joi.number().integer().min(0).max(100).default(0).messages({
    'any.required': 'bonus percentage is required.',
    'number.base': 'bonus percentage must be a number.',
    'number.min': 'bonus percentage cannot be negative.',
    'number.max': 'bonus percentage cannot exceed 100.',
  }),

  bonus_interval: Joi.string()
    .lowercase()
    .valid(...BONUS_INTERVAL_ARRAY)
    .default(BONUS_INTERVAL_ENUM.NULL)
    .messages({
      'any.required': 'bonus interval is required.',
      'string.base': 'bonus interval must be a string.',
      'any.only': `bonus interval must be one of: ${BONUS_INTERVAL_ARRAY.join(', ')}.`,
    }),

  bank_name: Joi.string().trim().lowercase().min(2).max(50).allow('').messages({
    'string.base': 'bank name must be a string.',
    'string.min': 'bank name must be at least {#limit} characters long.',
    'string.max': 'bank name must not exceed {#limit} characters.',
  }),

  bank_account_name: Joi.string()
    .trim()
    .lowercase()
    .min(2)
    .max(50)
    .allow('')
    .pattern(/^[\p{L}][\p{L} '-]*[\p{L}]$/u)
    .messages({
      'string.base': 'bank account name must be a string.',
      'string.min': 'bank account name must be at least {#limit} characters long.',
      'string.max': 'bank account name must not exceed {#limit} characters.',
      'string.pattern.base':
        'bank account name must contain only letters, spaces, apostrophes, or hyphens.',
    }),

  bank_account_number: Joi.string().trim().min(5).max(20).allow('').messages({
    'string.base': 'bank account number must be a string.',
    'string.min': 'bank account number must be at least {#limit} characters long.',
    'string.max': 'bank account number must not exceed {#limit} characters.',
  }),

  employment_status: Joi.string()
    .lowercase()
    .valid(...EMPLOYMENT_STATUS_ARRAY)
    .default(EMPLOYMENT_STATUS_ENUM.ACTIVE)
    .required()
    .messages({
      'any.required': 'employment status is required.',
      'string.base': 'employment status must be a string.',
      'any.only': `employment status must be one of: ${EMPLOYMENT_STATUS_ARRAY.join(', ')}.`,
    }),

  tax_code: Joi.string().trim().lowercase().min(2).max(20).allow('').messages({
    'string.base': 'tax code must be a string.',
    'string.min': 'tax code must be at least {#limit} characters long.',
    'string.max': 'tax code must not exceed {#limit} characters.',
  }),

  tax_rate: Joi.number().precision(2).min(0).default(0).messages({
    'number.base': 'tax rate must be a number.',
  }),

  tax_number: Joi.string().trim().min(5).max(20).allow('').messages({
    'string.base': 'tax number must be a string.',
    'string.min': 'tax number must be at least {#limit} characters long.',
    'string.max': 'tax number must not exceed {#limit} characters.',
  }),

  // pension: Joi.object({
  //   provider: Joi.string().trim().lowercase().min(2).max(30).required().messages({
  //     'string.base': 'pension provider must be a valid text.',
  //     'string.empty': 'pension provider is required.',
  //     'any.required': 'pension provider is required.',
  //     'string.min': 'pension provider must be at least {#limit} characters long.',
  //     'string.max': 'pension provider must not exceed {#limit} characters.',
  //   }),

  //   policy_number: Joi.string().trim().lowercase().min(2).max(30).required().messages({
  //     'string.base': 'policy number must be a valid text.',
  //     'string.empty': 'policy number is required.',
  //     'any.required': 'policy number is required.',
  //     'string.min': 'policy number must be at least {#limit} characters long.',
  //     'string.max': 'policy number must not exceed {#limit} characters.',
  //   }),

  //   start_date: Joi.date().iso().required().messages({
  //     'date.base': 'pension start date must be a valid date.',
  //     'date.format': 'pension start date must follow the iso 8601 format (yyyy-mm-dd).',
  //     'any.required': 'pension start date is required.',
  //   }),

  //   monthly_contribution: Joi.number().positive().precision(2).required().messages({
  //     'number.base': 'monthly contribution must be a valid number.',
  //     'number.positive': 'monthly contribution must be greater than zero.',
  //     'number.precision': 'monthly contribution can have at most two decimal places.',
  //     'any.required': 'monthly contribution is required.',
  //   }),

  //   beneficiary_first_name: Joi.string()
  //     .trim()
  //     .lowercase()
  //     .min(2)
  //     .max(30)
  //     .required()
  //     .pattern(/^[\p{L}][\p{L} '-]*[\p{L}]$/u)
  //     .messages({
  //       'string.base': 'beneficiary first name must be a valid text.',
  //       'string.empty': 'beneficiary first name is required.',
  //       'any.required': 'beneficiary first name is required.',
  //       'string.min': 'beneficiary first name must be at least {#limit} characters long.',
  //       'string.max': 'beneficiary first name must not exceed {#limit} characters.',
  //       'string.pattern.base':
  //         'beneficiary first name must contain only letters, spaces, apostrophes, or hyphens.',
  //     }),

  //   beneficiary_middle_name: Joi.string()
  //     .trim()
  //     .lowercase()
  //     .min(0)
  //     .max(30)
  //     .optional()
  //     .allow(null, '')
  //     .pattern(/^[\p{L} '-]*$/u)
  //     .messages({
  //       'string.base': 'beneficiary middle name must be a valid text.',
  //       'string.min': 'beneficiary middle name must be at least {#limit} characters long.',
  //       'string.max': 'beneficiary middle name must not exceed {#limit} characters.',
  //       'string.pattern.base':
  //         'beneficiary middle name must contain only letters, spaces, apostrophes, or hyphens.',
  //     }),

  //   beneficiary_last_name: Joi.string()
  //     .trim()
  //     .lowercase()
  //     .min(2)
  //     .max(50)
  //     .required()
  //     .pattern(/^[\p{L}][\p{L} '-]*[\p{L}]$/u)
  //     .messages({
  //       'string.base': 'beneficiary last name must be a valid text.',
  //       'string.empty': 'beneficiary last name is required.',
  //       'any.required': 'beneficiary last name is required.',
  //       'string.min': 'beneficiary last name must be at least {#limit} characters long.',
  //       'string.max': 'beneficiary last name must not exceed {#limit} characters.',
  //       'string.pattern.base':
  //         'beneficiary last name must contain only letters, spaces, apostrophes, or hyphens.',
  //     }),

  //   beneficiary_phone_number: Joi.string().trim().min(5).max(15).required().messages({
  //     'string.base': 'beneficiary phone number must be a valid text.',
  //     'string.pattern.base':
  //       "beneficiary phone number must be in a valid international format (10-15 digits, with an optional '+' prefix).",
  //     'string.empty': 'beneficiary phone number is required.',
  //     'any.required': 'beneficiary phone number is required.',
  //     'string.min': 'beneficiary phone number must be at least {#limit} characters long.',
  //     'string.max': 'beneficiary phone number must not exceed {#limit} characters.',
  //   }),

  //   beneficiary_relation: Joi.string().trim().lowercase().min(2).max(15).required().messages({
  //     'string.base': 'beneficiary relation must be a valid text.',
  //     'string.empty': 'beneficiary relation is required.',
  //     'any.required': 'beneficiary relation is required.',
  //     'string.min': 'beneficiary relation must be at least{#limit} characters long.',
  //     'string.max': 'beneficiary relation must not exceed {#limit} characters.',
  //   }),

  //   beneficiary_date_of_birth: Joi.date().iso().required().messages({
  //     'date.base': 'beneficiary date of birth must be a valid date.',
  //     'date.format': 'beneficiary date of birth must follow the iso 8601 format (yyyy-mm-dd).',
  //     'any.required': 'beneficiary date of birth is required.',
  //   }),
  // })
  //   .required()
  //   .messages({
  //     'object.base': 'pension details must be a valid object.',
  //     'any.required': 'pension details are required.',
  //   }),

  pension: createPensionSchema.optional().default('').allow(''),
})
  .custom(createEmployeeCustomValidator, 'Create Employee Custom Validator')
  .messages({
    'no.employment.end.date': 'employment end date is required for contract employment.',
    'no.bank.details': 'bank details are required for electronic payment.',
    'incomplete.bank.details': 'all bank details must be provided together.',
    'no.rate.or.work.hours': 'hourly rate and work hours per week are required for hourly payment.',
    'one.kind.of.payment': 'either salary or hourly rate with work hours can be provided not both.',
    'salary.required': 'salary must be provided if kind of payment is salary',
    'country.not.known': 'employee country is not known',
    'phone.number.not.valid': 'employee phone number is not valid for employee country',
  });

// create bulk employee schema
export const createMultipleEmployeeSchema = Joi.object<{
  employees: Partial<IEmployeeAttributes>[];
}>({
  employees: Joi.array()
    .items(createEmployeeSchema)
    .min(2)
    .required()
    .custom(createMultipleEmployeesCustomValidator)
    .messages({
      'array.base': 'employees must be an array of employees.',
      'array.min': 'you must provide at least {#limit} employees.',
      'array.includesRequiredUnknowns': 'each employee must follow the required schema.',
      'any.required': 'employees is required.',
      'duplicate.emails': '{{#count}} employees have the same email: {{#value}}',
      'duplicate.phoneNumbers': '{{#count}} employees have the same phone number: {{#value}}',
      'duplicate.nationalIds': '{{#count}} employees have the same national ID: {{#value}}',
      'duplicate.taxNumbers': '{{#count}} employees have the same tax number: {{#value}}',
      'duplicate.taxCodes': '{{#count}} employees have the same tax code: {{#value}}',
    }),
});

//edit employee schema
export interface IEditEmployeeAttributes {
  employee_details_update?: Partial<IEmployeeAttributes>;
  deductibles_update?: Partial<IDeductibleAttributes>[];
  pension_update?: Partial<IPensionAttributes>;
}
//edit employee schema
export const editEmployeeSchema = Joi.object<IEditEmployeeAttributes>({
  employee_details_update: makeSchemaFieldsOptional(createEmployeeSchema, {
    exclude: ['pension', 'Pension'],
  })
    .optional()
    .default('')
    .allow(''),

  deductibles_update: Joi.array()
    .items(
      makeSchemaFieldsOptional(createDeductibleSchema)
        .concat(
          Joi.object({
            id: Joi.string()
              .guid({ version: ['uuidv4'] })
              .optional()
              .messages({
                'string.guid': 'ID must be a valid UUID.',
              }),
          })
        )
        .custom(requireAtLeastOneField, 'require at least one field')
        .messages({ 'any.atLeastOneField': 'at least one deductible field is required.' })
    )
    .optional()
    .default('')
    .allow('')
    .min(1)
    .messages({ 'array.min': 'at least one deductible is required' }),

  pension_update: makeSchemaFieldsOptional(createPensionSchema).optional().default('').allow(''),
})
  .custom(editEmployeeCustomValidator)
  .messages({
    'any.required':
      'at least one of employee_details_update, pension_update, or deductibles_update must be provided.',
    'employee.details.empty': 'employee_details_update cannot be empty if present',
    'pension.details.empty': 'pension_update details cannot be empty if present',
  });
