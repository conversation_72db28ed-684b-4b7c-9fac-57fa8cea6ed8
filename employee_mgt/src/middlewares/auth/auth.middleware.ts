import { Request, Response, NextFunction } from 'express';
import UserAPI from '../../api/endpoints/user.apis';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { IUser, SUBSCRIPTION_STATUS } from '../../interfaces/user.interfaces';
import { NotAuthenticatedError, NotPermittedError } from '../../helpers/error.helpers';
import httpContext from 'express-http-context';
import { ERRORS } from '../../constants/errors.constants';
import { HTTP_METHODS } from '../../constants/values.constants';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user: IUser;
      orgSubIsActive: boolean;
    }
  }
}

export default class AuthMiddlewares extends RequestHandlerErrorWrapper {
  // authenticate employee
  async authenticateEmployee(req: Request, res: Response, next: NextFunction) {
    next();
  }

  // authenticate regular user
  async authenticateUser(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];

    if (!authHeader) throw new NotAuthenticatedError();

    httpContext.set('authHeader', authHeader);
    const authenticatedUser = await UserAPI.getMyAccount();

    if (!authenticatedUser) throw new NotAuthenticatedError();

    req.user = { ...authenticatedUser };

    if (req.user?.globalAccess || req.user?.isSuperAdmin) throw new NotPermittedError();

    const { organization, organizationMembers, ...userInfo } = req.user;

    if (!organization) throw new NotPermittedError(ERRORS.organizationIsRequiredError);

    const logUserDetails = {
      userId: req.user.id,
      orgId: req.user.organization.id,
      email: req.user.email,
    };

    res.locals = {
      ...res.locals,
      organization,
      organizationMembers,
      userInfo,
      requestLogDetails: { ...res.locals.requestLogDetails, userDetails: logUserDetails },
    };

    const { subscription } = organization;

    req.orgSubIsActive = subscription
      ? subscription?.access &&
        !subscription?.viewOnly &&
        subscription?.status === SUBSCRIPTION_STATUS.ACTIVE &&
        new Date(subscription.expiresAt) > new Date()
      : false;

    next();
  }

  // authenticate admin user.
  async authenticateAdminUser(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];

    if (!authHeader) throw new NotAuthenticatedError();

    httpContext.set('authHeader', authHeader);

    const authenticatedAdmin = await UserAPI.getMyAccount();

    if (!authenticatedAdmin) throw new NotAuthenticatedError();

    req.user = authenticatedAdmin;

    if (!req.user?.globalAccess || !req.user?.isSuperAdmin) throw new NotPermittedError();

    const logUserDetails = {
      userId: req.user.id,
      email: req.user.email,
    };

    res.locals = {
      ...res.locals,
      requestLogDetails: { ...res.locals.requestLogDetails, userDetails: logUserDetails },
    };

    next();
  }

  // check if subscription is active
  async validateActiveSubscription(req: Request, _res: Response, next: NextFunction) {
    if (req.method !== HTTP_METHODS.GET) {
      if (!req.user) {
        throw new NotAuthenticatedError();
      }

      if (!req.orgSubIsActive) {
        throw new NotPermittedError(ERRORS.requiresActiveSubscriptionError);
      }
    }

    next();
  }
}
