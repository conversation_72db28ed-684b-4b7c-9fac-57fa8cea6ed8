import AdminEmployeeControllers from '../app_users/admin/controllers/employee.admin.controller';
import OrgEmployeeDeductibleControllers from '../app_users/organization/controllers/deductible.controller';
import OrgEmployeeControllers from '../app_users/organization/controllers/employee.controller';
import OrgEmployeeLeaveControllers from '../app_users/organization/controllers/leave.controller';
import OrgEmployeePaymentControllers from '../app_users/organization/controllers/payment-history.controller';
import OrgEmployeePensionControllers from '../app_users/organization/controllers/pension.controller';
import AuthMiddlewares from '../middlewares/auth/auth.middleware';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import EmployeeDeductibleServices from '../services/deductibles.service';
import EmployeeServices from '../services/employee.service';
import EmployeeLeaveServices from '../services/leave.service';
import EmployeePaymentServices from '../services/payment-history.service';
import EmployeePensionServices from '../services/pension.service';
import UtilityControllers from '../utilities.controller';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';

//add all services instances typing
export interface ServiceInstances {
  employeeService: EmployeeServices;
  employeeLeaveService: EmployeeLeaveServices;
  employeePaymentService: EmployeePaymentServices;
  employeePensionService: EmployeePensionServices;
  employeeDeductibleService: EmployeeDeductibleServices;
  backgroundTaskManagers: BackgroundTaskManager;
}

//add all controllers instances typing
export interface ControllerInstances {
  utilityController: UtilityControllers;
  orgEmployeeController: OrgEmployeeControllers;
  orgEmployeeLeaveController: OrgEmployeeLeaveControllers;
  orgEmployeePaymentController: OrgEmployeePaymentControllers;
  orgEmployeePensionController: OrgEmployeePensionControllers;
  orgEmployeeDeductibleController: OrgEmployeeDeductibleControllers;
  adminEmployeeController: AdminEmployeeControllers;
}

//add all middleware instances typing
export interface MiddlewareInstances {
  authMiddleware: AuthMiddlewares;
  utilityMiddleware: UtilityMiddlewares;
}

export interface ContainerInstances {
  controllers: ControllerInstances;
  services: ServiceInstances;
  middlewares: MiddlewareInstances;
}
