import AdminEmployeeControllers from '../app_users/admin/controllers/employee.admin.controller';
import EmployeeDeductibleControllers from '../app_users/organization/controllers/deductible.controller';
import OrgEmployeeControllers from '../app_users/organization/controllers/employee.controller';
import OrgEmployeeLeaveControllers from '../app_users/organization/controllers/leave.controller';
import OrgEmployeePaymentControllers from '../app_users/organization/controllers/payment-history.controller';
import OrgEmployeePensionControllers from '../app_users/organization/controllers/pension.controller';
import UtilityControllers from '../utilities.controller';
import Container from './container.global';
import services from './services.container';

const controllers = new Container('controllers');

//register all controllers here
controllers.register('utilityController', new UtilityControllers());

controllers.register(
  'orgEmployeeController',
  new OrgEmployeeControllers(services.resolve('employeeService'))
);

controllers.register(
  'orgEmployeeLeaveController',
  new OrgEmployeeLeaveControllers(services.resolve('employeeLeaveService'))
);

controllers.register(
  'orgEmployeePaymentController',
  new OrgEmployeePaymentControllers(services.resolve('employeePaymentService'))
);

controllers.register(
  'orgEmployeePensionController',
  new OrgEmployeePensionControllers(services.resolve('employeePensionService'))
);

controllers.register(
  'orgEmployeeDeductibleController',
  new EmployeeDeductibleControllers(services.resolve('employeeDeductibleService'))
);

controllers.register(
  'adminEmployeeController',
  new AdminEmployeeControllers(services.resolve('employeeService'))
);

export default controllers;
