import { SERVICES } from '../constants/values.constants';
import Deductible, { IDeductibleAttributes } from '../models/deductible.model';
import sequelize from '../config/database/connection';
import { BulkCreateOptions, Transaction } from 'sequelize';
import BaseServices from '../base.service';

export default class EmployeeDeductibleServices extends BaseServices<Deductible> {
  private attributesToBeExcluded = ['organization_id', 'employee_id'];

  constructor() {
    super(Deductible, SERVICES.deductibles);
  }

  // get a single deductible for an employee with employee details
  async getOneDeductible(orgId: string, empId: string, deductibleId: string) {
    const deductible = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: deductibleId,
      },
      {
        attributes: { exclude: this.attributesToBeExcluded },
      },
      true
    )) as IDeductibleAttributes;

    return deductible;
  }

  // get all deductibles for an employee with pagination
  async getAllDeductibles(orgId: string, empId: string, offset = 0, limit = 50) {
    const result = (await this.getManyAndCount(
      { organization_id: orgId, employee_id: empId },
      { offset, limit, attributes: { exclude: this.attributesToBeExcluded } }
    )) as { rows: IDeductibleAttributes[]; count: number };

    return result;
  }

  // create multiple deductibles for an employee
  async createOneDeductible(
    orgId: string,
    empId: string,
    deductibles: Partial<IDeductibleAttributes>[]
  ) {
    // add organization and employee IDs to each deductible
    deductibles.forEach((deductible) => {
      deductible.organization_id = orgId;
      deductible.employee_id = empId;
    });

    // create deductibles in a transaction
    const result = await sequelize.transaction(async (transaction) => {
      const createdDeductibles = (await this.bulkCreate(deductibles, {
        transaction,
      })) as IDeductibleAttributes[];

      return createdDeductibles;
    });

    // remove internal fields from response
    result.forEach((deductible) => {
      delete deductible.organization_id;
      delete deductible.employee_id;
    });

    return result;
  }

  // delete a specific deductible for an employee
  async deleteOneDeductible(orgId: string, empId: string, deductibleId: string) {
    const deductible = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: deductibleId,
      },
      {},
      false
    )) as Deductible;

    await deductible.destroy();

    return true;
  }

  // update a specific deductible for an employee with employee details
  async updateOneDeductible(
    orgId: string,
    empId: string,
    deductibleId: string,
    updates: Partial<IDeductibleAttributes>
  ) {
    // get deductible with employee details for notification purposes
    const deductible = (await this.getOne(
      { organization_id: orgId, employee_id: empId, id: deductibleId },
      {},
      false
    )) as Deductible;

    // apply updates and save
    deductible.set(updates);
    const updatedDeductible = (await this.saveInstance(deductible)) as IDeductibleAttributes;

    return this.removeEmployeeAndOrganizationId(updatedDeductible);
  }

  async createMultipleDeductibles(
    data: Partial<IDeductibleAttributes>[],
    options: BulkCreateOptions = {},
    plain = true
  ) {
    return await this.bulkCreate(data, options, plain);
  }

  async externalServiceGetDeductible(
    orgId: string,
    empId: string,
    deductibleId: string,
    transaction: Transaction = null,
    plain: boolean = true
  ): Promise<IDeductibleAttributes | Deductible> {
    return await this.getOneOrNull(
      { organization_id: orgId, employee_id: empId, id: deductibleId },
      { transaction },
      plain
    );
  }

  async externalServiceGetDeductibles(
    orgId: string,
    empId: string,
    transaction: Transaction = null,
    plain: boolean = true
  ): Promise<(IDeductibleAttributes | Deductible)[]> {
    return await this.getMany(
      { organization_id: orgId, employee_id: empId },
      { transaction },
      plain
    );
  }

  async externalServiceBulkCreate(
    data: Partial<IDeductibleAttributes>[],
    options: BulkCreateOptions = {},
    plain = true
  ) {
    return await this.bulkCreate(data, options, plain);
  }
}
