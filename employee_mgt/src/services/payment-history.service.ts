import BaseServices from '../base.service';
import { SERVICES } from '../constants/values.constants';
import PaymentHistory, { IPaymentHistoryAttributes } from '../models/payment-history.model';

export default class EmployeePaymentServices extends BaseServices<PaymentHistory> {
  private attributesToExclude = ['organization_id', 'employee_id'];

  constructor() {
    super(PaymentHistory, SERVICES.paymentHistories);
  }

  private removeUnwantedPaymentHistoryData(data: IPaymentHistoryAttributes) {
    const copy = { ...data };

    delete copy.organization_id;
    delete copy.employee_id;

    return copy;
  }

  async getAllPaymentHistories(
    orgId: string,
    empId: string,
    offset?: number,
    limit?: number
  ): Promise<{ rows: IPaymentHistoryAttributes[]; count: number }> {
    return await this.getManyAndCount(
      { organization_id: orgId, employee_id: empId },
      {
        offset,
        limit,
        attributes: { exclude: this.attributesToExclude },
      },
      true
    );
  }

  async getOnePaymentHistory(
    orgId: string,
    empId: string,
    paymentHistoryId: string
  ): Promise<IPaymentHistoryAttributes> {
    const paymentHistory = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: paymentHistoryId,
      },
      { attributes: { exclude: this.attributesToExclude } }
    )) as IPaymentHistoryAttributes;

    return paymentHistory;
  }

  async createOnePaymentHistory(
    orgId: string,
    empId: string,
    paymentHistoryDetails: Partial<IPaymentHistoryAttributes>
  ): Promise<IPaymentHistoryAttributes> {
    paymentHistoryDetails.organization_id = orgId;
    paymentHistoryDetails.employee_id = empId;

    const createdPaymentHistory = (await this.create(
      paymentHistoryDetails,
      {},
      true
    )) as IPaymentHistoryAttributes;

    return createdPaymentHistory;
  }

  async editOnePaymentHistory(
    orgId: string,
    empId: string,
    paymentHistoryId: string,
    updates: Partial<IPaymentHistoryAttributes>
  ): Promise<IPaymentHistoryAttributes> {
    const paymentHistory = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: paymentHistoryId,
      },
      {},
      false
    )) as PaymentHistory;

    paymentHistory.set(updates);
    const updatedPaymentHistory = (await this.saveInstance(
      paymentHistory
    )) as IPaymentHistoryAttributes;

    delete updatedPaymentHistory.organization_id;
    delete updatedPaymentHistory.employee_id;

    return updatedPaymentHistory;
  }

  async deleteOnePaymentHistory(
    orgId: string,
    empId: string,
    paymentHistoryId: string
  ): Promise<boolean> {
    const paymentHistory = (await this.getOne(
      { organization_id: orgId, employee_id: empId, id: paymentHistoryId },
      {},
      false
    )) as PaymentHistory;

    await paymentHistory.destroy();
    return true;
  }
}
