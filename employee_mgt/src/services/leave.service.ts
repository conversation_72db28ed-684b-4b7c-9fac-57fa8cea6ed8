import Leave, { ILeaveAttributes } from '../models/leave.model';
import { BadRequestError, ConflictError } from '../helpers/error.helpers';
import { ERRORS } from '../constants/errors.constants';
import { LEAVE_APPROVAL_STATUS_ENUM, LEAVE_STATUS_ENUM } from '../models/enums';
import EmployeeLeaveServiceUtils from './utils/leave.service.utils';
import BaseServices from '../base.service';

export default class EmployeeLeaveServices extends BaseServices<Leave> {
  private attributesToExclude = ['organization_id', 'employee_id'];

  constructor() {
    super(Leave);
  }

  async getAllLeaves(orgId: string, empId: string, offset = 0, limit = 50) {
    return (await this.getManyAndCount(
      { organization_id: orgId, employee_id: empId },
      { attributes: { exclude: this.attributesToExclude }, offset, limit },
      true
    )) as {
      rows: ILeaveAttributes[];
      count: number;
    };
  }

  async getOneLeave(orgId: string, empId: string, leaveId: string) {
    return (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: leaveId,
      },
      { attributes: { exclude: this.attributesToExclude } }
    )) as ILeaveAttributes;
  }

  async approveOrRejectLeave(
    orgId: string,
    empId: string,
    leaveId: string,
    approvalStatus: string
  ) {
    const existingLeave = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: leaveId,
      },
      {},
      false
    )) as Leave;

    if (existingLeave.status !== LEAVE_STATUS_ENUM.AWAITING_APPROVAL) {
      throw new BadRequestError(ERRORS.leaveIsNotAwaitingApproval);
    }

    existingLeave.status = EmployeeLeaveServiceUtils.determineLeaveApprovalStatus(
      approvalStatus as LEAVE_APPROVAL_STATUS_ENUM,
      existingLeave.start_date
    );

    const approvedOrRejectedLeave = (await this.saveInstance(existingLeave)) as ILeaveAttributes;

    delete approvedOrRejectedLeave.organization_id;
    delete approvedOrRejectedLeave.employee_id;

    return approvedOrRejectedLeave;
  }

  async cancelLeave(orgId: string, empId: string, leaveId: string) {
    const existingLeave = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: leaveId,
      },
      {},
      false
    )) as Leave;

    EmployeeLeaveServiceUtils.validateLeaveCancelling(existingLeave.status);

    existingLeave.set({ status: LEAVE_STATUS_ENUM.CANCELLED });
    const cancelledLeave = await this.saveInstance(existingLeave);

    return cancelledLeave;
  }

  async createOneLeave(orgId: string, empId: string, leaveDetails: Partial<ILeaveAttributes>) {
    leaveDetails = EmployeeLeaveServiceUtils.addCreateLeaveDetails(leaveDetails, orgId, empId);
    const createdLeave = (await this.create(leaveDetails, {}, true)) as ILeaveAttributes;

    return createdLeave;
  }

  async editOneLeave(
    orgId: string,
    empId: string,
    leaveId: string,
    leaveChanges: Partial<ILeaveAttributes>
  ): Promise<Partial<ILeaveAttributes>> {
    const existingLeave = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: leaveId,
      },
      {},
      false
    )) as Leave;

    if (existingLeave.status !== LEAVE_STATUS_ENUM.AWAITING_APPROVAL) {
      throw new ConflictError(ERRORS.leaveCannotBeEdited);
    }

    leaveChanges = EmployeeLeaveServiceUtils.addEditLeaveDetails(leaveChanges);
    existingLeave.set(leaveChanges);

    const editedLeave = (await this.saveInstance(existingLeave)) as ILeaveAttributes;

    delete editedLeave.organization_id;
    delete editedLeave.employee_id;

    return editedLeave;
  }

  async autoTransitLeaveStatuses(transitionTime: Date) {
    const leaves = (await this.getManyAndCount(
      {
        status: [LEAVE_STATUS_ENUM.PENDING, LEAVE_STATUS_ENUM.IN_PROGRESS],
      },
      {},
      false
    )) as { rows: Leave[]; count: number };

    const updates = [];

    for (const leave of leaves.rows) {
      const status = leave.status;
      const startDate = new Date(leave.start_date);
      const endDate = new Date(leave.end_date);

      if (
        status === LEAVE_STATUS_ENUM.PENDING &&
        transitionTime >= startDate &&
        transitionTime <= endDate
      ) {
        leave.set({ status: LEAVE_STATUS_ENUM.IN_PROGRESS });
        updates.push(leave.save({ fields: ['status'] }));
      }

      if (status === LEAVE_STATUS_ENUM.IN_PROGRESS && transitionTime >= endDate) {
        leave.set({ status: LEAVE_STATUS_ENUM.COMPLETED });
        updates.push(leave.save({ fields: ['status'] }));
      }
    }

    if (updates.length > 0) {
      await Promise.allSettled(updates);
    }

    return updates.length;
  }
}
