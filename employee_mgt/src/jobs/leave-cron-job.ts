import cron from 'node-cron';
import logger from '../utilities/logger';
import { startOfDay } from 'date-fns';
import services from '../containers/services.container';
import * as Sentry from '@sentry/node';

logger.info({ message: '🕛 leave status auto update cron job initialized' });

// everyday at 12:00am
cron.schedule('0 0 * * *', async () => {
  try {
    logger.info({ message: 'cron job running: checking for leaves to be transitioned...' });

    const now = startOfDay(new Date());

    const count = await services.resolve('employeeLeaveService').autoTransitLeaveStatuses(now);

    logger.info({
      message: `leave status update completed, update ${count}  leaves statues`,
      time: new Date().toISOString(),
    });
  } catch (error) {
    Sentry.captureException(error, { level: 'fatal' });
  }
});
