import { Request } from 'express';
import RequestIP from 'request-ip';
import { v4 as uuidv4 } from 'uuid';
import { BASE_10, FILE_EXTENSION } from '../constants/values.constants';
import geoip from 'geoip-lite';
import { toZonedTime } from 'date-fns-tz';
import { isDevelopmentEnv, isProductionEnv } from './guards';
import jwt from 'jsonwebtoken';
import { Response } from 'express';
import { NotificationAttributes, TaskPriority } from '../types/global.types';
import { NOTIFICATION_EVENT_NAMES } from '../constants/values.constants';
import { NOTIFICATION_COLLECTIONS } from '../constants/notification.constants';
import services from '../containers/services.container';
import { OrganizationMember } from '../interfaces/user.interfaces';

export const getUserAgentHeader = (req: Request) => req.headers['user-agent'] || 'unknown agent';

export function capitalizeFirstLetter(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export const generateToken = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

export const genReference = () => {
  const reference = uuidv4().slice(0, 12).split('-').join('');
  return reference;
};

export const formatDateToYearMonthDay = (dateValue: Date) => {
  const date = new Date(dateValue);
  const options: any = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  };
  const formattedDate = date.toLocaleString('en-GB', options).replace(',', '');
  if (formattedDate === 'Invalid Date') return null;
  return formattedDate;
};

export const pagination = (req: Request): { offset: number; limit: number } => {
  const { offset = 1, limit = 50 } = req.query;

  let parsedPerPage: number = typeof limit === 'string' ? parseInt(limit) : (limit as number);
  if (isNaN(parsedPerPage)) {
    parsedPerPage = 50;
  }

  let parsedPage: number = typeof offset === 'string' ? parseInt(offset) : (offset as number);
  if (isNaN(parsedPage)) {
    parsedPage = 1;
  }
  const paginate = {
    offset: (parsedPage - 1) * parsedPerPage,
    limit: parsedPerPage,
  };
  return paginate;
};

export const getFileName = (filetype: string, filename: string): string | undefined => {
  if (filetype === 'pdf') return filename + '.' + FILE_EXTENSION.PDF;
};

export const removeWhiteSpace = (word: string): string | undefined => {
  if (!word) return;
  return word.replace(/\s+/g, '');
};

export const trimAndLowerCase = (word: string): string => {
  if (!word) return '';
  return word.trim().toLowerCase();
};

export const getPublicAddress = (req: Request): string => {
  const xForwardedFor = req.header('x-forwarded-for');
  const ip =
    (xForwardedFor ? xForwardedFor.split(',')[0].trim() : null) ||
    RequestIP.getClientIp(req) ||
    req.ips[0] ||
    req.ip;
  if (!ip) return '';
  if (ip === '::1' || ip === '127.0.0.1' || ip.startsWith('::ffff:')) {
    return 'localhost';
  }
  return ip;
};

export const convertHexToRgba = (hex: string, opacity: number) => {
  if (hex && opacity) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
};

export const formatDateToYearMonthDayTime = (dateValue: Date, userTimezone: string) => {
  if (!dateValue) return null;
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true, // Enables AM/PM format
    timeZone: userTimezone, // Ensures consistent time formatting
  };

  const formattedDate = new Date(dateValue).toLocaleString('en-GB', options).replace(',', '');
  if (formattedDate === 'Invalid Date') return null;
  return formattedDate;
};

export const getZonedTimeStamp = (req: Request) => {
  const ip = getPublicAddress(req) || '0.0.0.0';
  const geo = geoip.lookup(ip);
  const userTimezone = geo?.timezone || 'UTC';
  const date = new Date();
  const zonedDateTime = toZonedTime(date, userTimezone);
  return zonedDateTime;
};

export const getUserLocation = (req: Request) => {
  const ip = getPublicAddress(req) || '0.0.0.0';
  const geo = geoip.lookup(ip);
  const countryCode: string = geo?.country || 'US';
  return countryCode;
};

export const getUserTimeZone = (req: Request): string => {
  const ip = getPublicAddress(req) || '0.0.0.0';
  const geo = geoip.lookup(ip);
  const userTimezone = geo?.timezone || 'UTC';
  return userTimezone;
};

// export const isValidDate = (value: any): boolean => {
//   return (
//     value instanceof Date ||
//     ((typeof value === 'string' || typeof value === 'number') && !isNaN(new Date(value).getTime()))
//   );
// };

//convert created at and updated at time to user timezone
export const convertCreateAtAndUpdatedAtToUserTimezone = (
  data: any,
  timezone: string = 'UTC'
): any => {
  if (Array.isArray(data) && data.length <= 0) return data;

  if (typeof data === 'object' && Object.keys(data).length <= 0) return data;

  if (Array.isArray(data)) {
    return data.map((item) => convertCreateAtAndUpdatedAtToUserTimezone(item, timezone));
  }

  if (data !== null && typeof data === 'object') {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => {
        const toBeConverted = ['createdAt', 'updatedAt', 'created_at', 'updated_at'];

        if (toBeConverted.includes(key)) {
          return [
            key,
            typeof value === 'string' && !isNaN(Date.parse(value))
              ? toZonedTime(new Date(value), timezone)
              : value,
          ];
        }

        if (Array.isArray(value)) {
          return [
            key,
            value.map((item) => convertCreateAtAndUpdatedAtToUserTimezone(item, timezone)),
          ];
        }

        return [key, value];
      })
    );
  }

  return data;
};

//convert all dates to user specific time zone
export const convertAllDatesToTimezone = (data: any, timeZone: string = 'UTC'): any => {
  if (Array.isArray(data) && data.length <= 0) return data;

  if (typeof data === 'object' && Object.entries(data).length <= 0) return data;

  if (Array.isArray(data)) {
    return data.map((item) => convertAllDatesToTimezone(item, timeZone));
  }

  if (data !== null && typeof data === 'object') {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => {
        if (typeof value === 'string' && !isNaN(Date.parse(value))) {
          // Convert if it's a valid date string
          return [key, toZonedTime(new Date(value), timeZone)];
        }

        if (Array.isArray(value)) {
          return [key, value.map((item) => convertAllDatesToTimezone(item, timeZone))];
        }

        if (typeof value === 'object' && value !== null) {
          return [key, convertAllDatesToTimezone(value, timeZone)];
        }

        return [key, value];
      })
    );
  }

  return data;
};

// convert given time to specified time zone or utc by default
export const convertTimeToGivenTimeZone = (date: Date, timeZone: string = 'UTC'): Date => {
  if (!date || date === undefined) return;
  return toZonedTime(new Date(date), timeZone);
};

export const convertDbResultTimestampToUserTimezone = <T extends Model<any>>(
  data: T | T[],
  userTimeZone: string
): InferAttributes<T> | InferAttributes<T>[] => {
  if (Array.isArray(data)) {
    return data.map((item) => convertSingleDbResultTime(item, userTimeZone));
  } else {
    return convertSingleDbResultTime(data, userTimeZone);
  }
};

// Function to handle single record
const convertSingleDbResultTime = <T extends Model<any>>(
  item: T,
  userTimeZone: string
): InferAttributes<T> => {
  const plainItem = item.toJSON() as InferAttributes<T> & { created_at?: Date; updated_at?: Date };

  return {
    ...plainItem,
    ...(plainItem.created_at && {
      created_at: convertTimeToGivenTimeZone(new Date(plainItem.created_at), userTimeZone),
    }),
    ...(plainItem.updated_at && {
      updated_at: convertTimeToGivenTimeZone(new Date(plainItem.updated_at), userTimeZone),
    }),
  };
};

export const WEB_EVENTS_URL =
  isProductionEnv || isDevelopmentEnv
    ? process.env.WEB_EVENT_URL
    : 'http://127.0.0.1:4000/webevents/notification';

export const BASE_URL = isProductionEnv ? process.env.SERVER_PROD_URL : process.env.SERVER_DEV_URL;

export const LOCALHOST = `http:127.0.0.1:${process.env.PORT || 8752}`;
export const GOOGLE_API_BASE_URL = 'https://www.googleapis.com';
export const GOOGLE_ACCOUNT_BASE_URL = 'https://accounts.google.com/o/oauth2/v2/auth';

export const getUpdatedFields = <T extends object>(
  existingData: T,
  updatedData: Partial<T>
): Partial<T> =>
  Object.fromEntries(
    Object.entries(updatedData).filter(([key, value]) => existingData[key as keyof T] !== value)
  ) as Partial<T>;

export function getPagination(req: Request): { page: number; offset: number; limit: number } {
  const defaultPage = 1;
  const defaultLimit = 50;
  const maxPaginationLimit = 100;

  const rawPage = req.query.page;
  const rawLimit = req.query.limit;

  const page = parseInt(typeof rawPage === 'string' ? rawPage : '', BASE_10);
  const limit = parseInt(typeof rawLimit === 'string' ? rawLimit : '', BASE_10);

  const isValidPage = Number.isInteger(page) && page > 0;
  const isValidLimit = Number.isInteger(limit) && limit > 0;

  const safeLimit = isValidLimit ? Math.min(limit, maxPaginationLimit) : defaultLimit;
  const safePage = isValidPage ? page : defaultPage;
  const offset = (safePage - 1) * safeLimit;

  return {
    page: safePage,
    limit: safeLimit,
    offset,
  };
}

export const transformUserBusinessFields = (user: any | any[]): any | any[] => {
  const transformBusinessFields = (user) => {
    if (!user || !user.Business) return user;

    const business = user.Business;
    const transformedBusinessFields = Object.entries(business).reduce((acc, [key, value]) => {
      const camelKey = key.charAt(0).toLowerCase() + key.slice(1); // Ensure camelCase
      const prefixedKey = `business${camelKey.charAt(0).toUpperCase()}${camelKey.slice(1)}`;
      acc[prefixedKey] = value;
      return acc;
    }, {});

    delete user.Business;
    return { ...user, ...transformedBusinessFields };
  };

  if (Array.isArray(user)) {
    return user.map(transformBusinessFields);
  }
  return transformBusinessFields(user);
};

import {
  differenceInYears,
  differenceInMonths,
  differenceInWeeks,
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  differenceInSeconds,
  startOfDay,
  endOfDay,
} from 'date-fns';
import { InferAttributes, Model } from 'sequelize';
import { catchError } from './catch-async-error';
import path from 'path';
import fs from 'fs';
import Handlebars from 'handlebars';
import {
  EmailFormOptions,
  IMicroServiceInternalAuthPayload,
} from '../interfaces/global.interfaces';
import FormData from 'form-data';

export function getTimeDifferenceWithUnit(startDate: Date | string, endDate: Date | string) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    throw new Error('Invalid date format');
  }

  let value: number;
  let unit: string;

  if ((value = differenceInYears(end, start)) >= 1) {
    unit = 'year';
  } else if ((value = differenceInMonths(end, start)) >= 1) {
    unit = 'month';
  } else if ((value = differenceInWeeks(end, start)) >= 1) {
    unit = 'week';
  } else if ((value = differenceInDays(end, start)) >= 1) {
    unit = 'day';
  } else if ((value = differenceInHours(end, start)) >= 1) {
    unit = 'hour';
  } else if ((value = differenceInMinutes(end, start)) >= 1) {
    unit = 'minute';
  } else {
    value = differenceInSeconds(end, start);
    unit = 'second';
  }

  return {
    value: Number(value.toFixed(2)),
    unit: value > 1 ? `${unit}s` : unit,
  };
}

// get email html from template file
const getEmailHtml = catchError((source: string): string => {
  const filePath = path.resolve(`templates/emails/${source}.hbs`);
  return fs.readFileSync(filePath, 'utf8');
});

// compile email templates
export const compileEmailHtml = catchError(
  (sourceFile: string, data: any, templateLayout: string): string => {
    const layout = fs.readFileSync(
      path.resolve(`templates/emails/layouts/${templateLayout}.hbs`),
      'utf8'
    );
    const html = getEmailHtml(sourceFile);
    const template = Handlebars.compile(html);
    const contentHtml = template(data);
    const layoutTemplate = Handlebars.compile(layout);
    return layoutTemplate({ ...data, body: contentHtml });
  }
);

// generate email form
export const generateEmailForm = catchError((options: EmailFormOptions) => {
  const { to, subject, html, attachments } = options;

  const form = new FormData();

  form.append('from', process.env.NO_REPLY_EMAIL_USERNAME);
  form.append('to', to);
  form.append('subject', subject);
  form.append('html', html);
  if (attachments && attachments.length > 0) {
    attachments.forEach((attachment) => {
      form.append('attachments', attachment.buffer, { filename: attachment.filename });
    });
  }

  return form;
});

//validate internal auth tokens
export const verifyInternalAuthToken = catchError((token: string) => {
  const SECRET_KEY = process.env.INTERNAL_JWT_SECRET as string;

  return jwt.verify(token, SECRET_KEY) as IMicroServiceInternalAuthPayload;
});

// process the notification data
function processUserNotificationData(
  title: string,
  message: string,
  userId: string,
  orgId: string,
  userIds: string[],
  priority: TaskPriority = 'MEDIUM',
  excludedUsers: string[] = [],
  collection: NotificationAttributes['collection'] = NOTIFICATION_COLLECTIONS.EMPLOYEES
): NotificationAttributes {
  return {
    user_id: userId,
    org_id: orgId,
    user_ids: userIds,
    title,
    message,
    type: 'ORGANISATION',
    priority,
    event_name: NOTIFICATION_EVENT_NAMES.app,
    collection,
    exclude_users: excludedUsers,
  };
}

// send the notification data to the notification queue for processing
export async function sendUserNotification(
  res: Response,
  title: string,
  message: string,
  priority: TaskPriority = 'MEDIUM',
  excludedUsers = [],
  collection: NotificationAttributes['collection'] = NOTIFICATION_COLLECTIONS.EMPLOYEES
) {
  const userId = res.locals.userInfo.id;
  const orgId = res.locals.orgId as string;

  const orgMembers = res.locals.orgMembers as OrganizationMember[];
  const orgMembersUserIds = orgMembers.map((member) => member.userId);
  const uniqueUserIds = [...new Set(orgMembersUserIds)];
  const filteredUserIds = uniqueUserIds.filter((userId) => !excludedUsers.includes(userId));

  const data = processUserNotificationData(
    title,
    message,
    userId,
    orgId,
    filteredUserIds,
    priority,
    excludedUsers,
    collection
  );
  if (excludedUsers.length === 0) delete data.exclude_users;

  const taskManager = services.resolve('backgroundTaskManagers');
  return await taskManager.queueTasks(data, 'appNotificationQueue');
}

export function startOfTheDay(date: Date) {
  return startOfDay(date);
}

export function endOfTheDay(date: Date) {
  return endOfDay(date);
}

// process the notification data
function processOrganizationNotificationData(
  title: string,
  message: string,
  userId: string,
  orgId: string,
  userIds: string[],
  priority: TaskPriority = 'MEDIUM',
  collection: NotificationAttributes['collection'] = NOTIFICATION_COLLECTIONS.EMPLOYEES,
  excludedUsers: string[] = []
): NotificationAttributes {
  return {
    user_id: userId,
    org_id: orgId,
    user_ids: userIds,
    title,
    message,
    priority,
    type: 'ORGANISATION',
    event_name: NOTIFICATION_EVENT_NAMES.app,
    collection,
    exclude_users: excludedUsers,
  };
}

// send the notification data to the notification queue for processing
export async function sendOrganizationNotification(
  res: Response,
  title: string,
  message: string,
  priority: TaskPriority = 'MEDIUM',
  collection: NotificationAttributes['collection'] = NOTIFICATION_COLLECTIONS.EMPLOYEES,
  excludedUsers = []
) {
  const userId = res.locals.userInfo.id as string;
  const orgId = res.locals.orgId as string;

  const orgMembers = res.locals.orgMembers as OrganizationMember[];
  const orgMembersUserIds = orgMembers.map((member) => member.userId);
  const uniqueUserIds = [...new Set(orgMembersUserIds)];
  const filteredUserIds = uniqueUserIds.filter((userId) => !excludedUsers.includes(userId));

  const data = processOrganizationNotificationData(
    title,
    message,
    userId,
    orgId,
    filteredUserIds,
    priority,
    collection,
    excludedUsers
  );
  if (excludedUsers.length === 0) delete data.exclude_users;

  const taskManager = services.resolve('backgroundTaskManagers');
  return await taskManager.queueTasks(data, 'appNotificationQueue');
}
