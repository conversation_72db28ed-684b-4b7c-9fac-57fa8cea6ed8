import { Router } from 'express';
import { API_VERSION } from '../constants/values.constants';
import utilityRouter from './utilities.routes';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';
import { RateLimiters } from '../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../utilities/guards';
import adminRouter from '../app_users/admin/routes/index.routes';
import orgRouter from '../app_users/organization/routes/index.routes';

const globalRouter = Router({ mergeParams: true });

const { captureAppDetails } = middlewares.resolve('utilityMiddleware');
globalRouter.use(captureAppDetails);

if (isProductionEnv) {
  globalRouter.use(RateLimiters.global);
}

globalRouter.use(`${API_VERSION}/employees`, utilityRouter);

globalRouter.use(`${API_VERSION}/admin/employees`, adminRouter);

// globalRouter.use(`${API_VERSION}/employees/me`, empRouter);

globalRouter.use(`${API_VERSION}/employees`, orgRouter);

globalRouter.all('*', controllers.resolve('utilityController').resourceNotFound);

export default globalRouter;
