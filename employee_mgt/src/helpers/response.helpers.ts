import { Response } from 'express';
import StatusCode, { StatusCodes } from 'http-status-codes';
import fs from 'fs';
import logger from '../utilities/logger';
import * as Sentry from '@sentry/node';
import { ERRORS } from '../constants/errors.constants';
import { LogDetails, RequestLogDetails } from '../interfaces/log.interfaces';
import services from '../containers/services.container';
import { AppError } from './error.helpers';
import { getErrorCode } from './error.helpers';
import { isValuePresent } from '../utilities/guards';
import { Meta } from '../interfaces/global.interfaces';

interface ResponseMeta extends Meta {
  totalPages?: number;
}

type FileResponseOptions =
  | { filename: string; buffer: Buffer; filepath?: never }
  | { filename: string; filepath: string; buffer?: never };

// process log details
function processLogDetails(
  requestLogDetails: RequestLogDetails,
  action: string,
  statusCode: number,
  message: string,
  data?: any
): LogDetails {
  return {
    anonymous: requestLogDetails.userDetails?.userId ? false : true,
    userId: requestLogDetails?.userDetails?.userId ?? '',
    orgId: requestLogDetails?.userDetails?.orgId ?? '',
    action,
    details: {
      ...requestLogDetails,
      responseDetails: {
        statusCode,
        message,
        data,
      },
    },
  };
}

// send log to utilities
async function sendLog(
  res: Response,
  action: string,
  statusCode: number,
  message: string,
  data?: any
) {
  const requestLogDetails = res.locals.requestLogDetails as RequestLogDetails;
  const logDetails = processLogDetails(requestLogDetails, action, statusCode, message, data);

  await services.resolve('backgroundTaskManagers').queueTasks(logDetails, 'saveRequestLogsQueue');
}

// send buffer files
function sendBufferFile(res: Response, filename: string, fileBuffer: Buffer) {
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.setHeader('Content-Type', 'application/octet-stream');
  res.setHeader('Content-Length', fileBuffer.length);

  res.end(fileBuffer);
}

// send files saved on DISK
function sendDiskFile(res: Response, filepath: string, filename: string) {
  const stream = fs.createReadStream(filepath);

  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.setHeader('Content-Type', 'application/octet-stream');

  stream.pipe(res);

  stream.on('error', (err) => {
    Sentry.captureException(err);

    logger.error({
      name: 'Stream Error:',
      location: 'sending file as stream for download',
      error: err,
    });

    return res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json({
        status: 'error',
        message: ERRORS.fileDownloadingError,
        code: getErrorCode(StatusCodes.INTERNAL_SERVER_ERROR),
      })
      .end();
  });

  stream.on('end', () => {
    fs.unlink(filepath, (err) => {
      if (err) {
        Sentry.captureException(err);
        logger.error({
          name: 'File Deletion Error',
          location: 'deleting file from drive after downloading',
          err,
        });
      }
    });
  });
}

// sending success message for successful request
export function sendJsonResponse(
  res: Response,
  resMsgAndCode: [string, number],
  action: string,
  data?: any,
  meta?: Meta
) {
  // send log after response is sent
  process.nextTick(() => {
    sendLog(res, action, resMsgAndCode[1], resMsgAndCode[0], data).catch((error) => {
      Sentry.captureException(error);
    });
  });

  if (resMsgAndCode[1] === StatusCode.NO_CONTENT) return res.status(StatusCodes.NO_CONTENT).end();

  let responseMeta: ResponseMeta;

  if (isValuePresent(meta) && Object.keys(meta).length !== 0) {
    const { count, limit, page, totalCounts, ...otherMetaData } = meta;

    let totalPages: number;

    if (isValuePresent(totalCounts) && isValuePresent(limit))
      totalPages = Math.ceil(meta.totalCounts / meta.limit);

    responseMeta = {
      count,
      page,
      totalPages,
      ...otherMetaData,
    };
  }

  return res
    .status(resMsgAndCode[1])
    .json({
      status: 'success',
      message: resMsgAndCode[0],
      code: `S${resMsgAndCode[1]}`,
      data,
      meta: responseMeta,
    })
    .end();
}

// sending file as response
export function sendFileResponse(res: Response, action: string, options: FileResponseOptions) {
  process.nextTick(() => {
    sendLog(res, action, 200, `${options.filename} was potentially downloaded successfully`).catch(
      (error) => {
        Sentry.captureException(error);
      }
    );
  });

  if (options.filepath && options.filename) {
    sendDiskFile(res, options.filepath, options.filename);
    return;
  }

  return sendBufferFile(res, options.filename, options.buffer);
}

// sending error response
export function sendErrorResponse(res: Response, error: AppError) {
  return res
    .status(error.statusCode)
    .json({ status: error.status, message: error.message, code: getErrorCode(error.statusCode) })
    .end();
}

// send error with Data
export function sendErrorWithData(res: Response, statusCode: number, message?: string, data?: any) {
  return res
    .status(statusCode)
    .json({ message, code: getErrorCode(statusCode), details: data })
    .end();
}
