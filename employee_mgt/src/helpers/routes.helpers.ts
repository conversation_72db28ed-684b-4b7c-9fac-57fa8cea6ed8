import { Router } from 'express';
import controllers from '../containers/controllers.container';
import { ControllerInstances } from '../containers/container.interfaces';

export const instantiateRouter = <C extends keyof ControllerInstances>(controllerName: C) => {
  const router = Router({ mergeParams: true });
  const controller = controllers.resolve(controllerName) as ControllerInstances[C];

  return { router, controller };
};
