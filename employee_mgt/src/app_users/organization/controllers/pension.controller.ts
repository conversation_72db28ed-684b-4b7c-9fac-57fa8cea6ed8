/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response } from 'express';
import { PENSION } from '../../../constants/notification.constants';
import { RESPONSES } from '../../../constants/responses.constants';
import { USER_ACTIONS } from '../../../constants/values.constants';
import { IPensionAttributes } from '../../../models/pension.model';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import EmployeePensionServices from '../../../services/pension.service';
import { IEmployeeAttributes } from '../../../models/employee.model';
import { sendJsonResponse } from '../../../helpers/response.helpers';
import { sendOrganizationNotification } from '../../../utilities/global.utilities';

export default class OrgEmployeePensionControllers extends RequestHandlerErrorWrapper {
  constructor(private pensionService: EmployeePensionServices) {
    super();
  }

  async getPension(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const pensionId = req.params.pensionId;

    const pension = await this.pensionService.getOnePension(orgId, employee.id, pensionId);

    return sendJsonResponse(res, RESPONSES.pensionRetrieved, USER_ACTIONS.getPension, pension);
  }

  // async getOnePension(req: Request, res: Response) {
  //   const orgId = res.locals.orgId as string;
  //   const employee = res.locals.employee as IEmployeeAttributes;
  //   const pensionId = req.params.pensionId;

  //   const pension = await this.pensionService.getOnePension(orgId, employee.id, pensionId);

  //   return successResponse(res, RESPONSES.pensionRetrieved, USER_ACTIONS.getPension, pension);
  // }

  async createOnePension(req: Request, res: Response) {
    const orgId = res.locals.organizationId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const pensionData = req.body as Partial<IPensionAttributes>;

    const createdPension = await this.pensionService.createOnePension(
      orgId,
      employee.id,
      pensionData
    );

    await sendOrganizationNotification(
      res,
      'Pension created',
      `${req.user.email} has created a pension record for employee: ${employee?.first_name} ${employee?.last_name}`,
      'MEDIUM',
      PENSION
    );

    // Commented out caching-related code
    // await this.pensionService.clearCachedData(organizationId);
    return sendJsonResponse(
      res,
      RESPONSES.pensionCreated,
      USER_ACTIONS.createPension,
      createdPension
    );
  }

  async editOnePension(req: Request, res: Response) {
    const orgId = res.locals.organizationId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const pensionId = req.params.pensionId;
    const pensionData = req.body as Partial<IPensionAttributes>;

    const pension = await this.pensionService.editOnePension(
      orgId,
      employee.id,
      pensionId,
      pensionData
    );

    await sendOrganizationNotification(
      res,
      'Pension updated',
      `${req.user.email} has updated a pension record for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      PENSION
    );

    // Commented out caching-related code
    // await this.pensionService.clearCachedData(organizationId);
    return sendJsonResponse(res, RESPONSES.pensionUpdated, USER_ACTIONS.editPension, pension);
  }

  async deleteOnePension(req: Request, res: Response) {
    const orgId = res.locals.organizationId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const pensionId = req.params.pensionId;

    await this.pensionService.deleteOnePension(orgId, employee.id, pensionId);

    await sendOrganizationNotification(
      res,
      'Pension deleted',
      `${req.user.email} has deleted a pension record for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      PENSION
    );

    // Commented out caching-related code
    // await this.pensionService.clearCachedData(organizationId);
    return sendJsonResponse(res, RESPONSES.pensionDeleted, USER_ACTIONS.deletePension);
  }
}
