import { Request, Response } from 'express';
import EmployeeService from '../../../services/employee.service';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import { IEmployeeAttributes } from '../../../models/employee.model';
import { getPagination, sendOrganizationNotification } from '../../../utilities/global.utilities';
import {
  sendErrorWithData,
  sendFileResponse,
  sendJsonResponse,
} from '../../../helpers/response.helpers';
import { RESPONSES } from '../../../constants/responses.constants';
import { CREATE_EMPLOYEE_TEMPLATE, USER_ACTIONS } from '../../../constants/values.constants';
import { StatusCodes } from 'http-status-codes';
import { Meta } from '../../../interfaces/global.interfaces';
import { IEditEmployeeAttributes } from '../../../middlewares/validators/schemas/request_body/employee.schema';

export default class OrgEmployeeControllers extends RequestHandlerErrorWrapper {
  constructor(private employeeService: EmployeeService) {
    super();
  }

  async createOneEmployee(req: Request, res: Response) {
    const { orgId } = res.locals;
    const employeeDetails = req.body as Partial<IEmployeeAttributes>;

    const createdEmployee = await this.employeeService.createOneEmployee(orgId, employeeDetails);

    await sendOrganizationNotification(
      res,
      'Employee created',
      `Employee: ${createdEmployee.first_name} ${createdEmployee.last_name} was created`
    );

    // await this.employeeService.clearCachedData(orgId);
    return sendJsonResponse(
      res,
      RESPONSES.employeesCreated,
      USER_ACTIONS.createEmployee,
      createdEmployee
    );
  }

  async createMultipleEmployees(req: Request, res: Response) {
    const { orgId } = res.locals;
    const payload = req.body as { employees: Partial<IEmployeeAttributes>[] };

    const result = await this.employeeService.createMultipleEmployees(orgId, payload.employees);

    if (!result.created && result.conflicts)
      return sendErrorWithData(
        res,
        StatusCodes.CONFLICT,
        'employee(s) with some details already exists',
        result.conflicts
      );

    const createdEmployees = result.employees;

    await sendOrganizationNotification(
      res,
      'Multiple Employees Created',
      `${createdEmployees.length} employees created.`
    );

    return sendJsonResponse(
      res,
      RESPONSES.employeesCreated,
      USER_ACTIONS.createBulkEmployees,
      createdEmployees
    );
  }

  async getAllEmployees(req: Request, res: Response) {
    const { orgId } = res.locals;
    const { page, offset, limit } = getPagination(req);

    const result = await this.employeeService.getAllEmployees(orgId, offset, limit);

    const meta: Meta = {
      count: result.rows.length,
      page,
      limit,
      totalCounts: result.count,
    };

    return sendJsonResponse(
      res,
      RESPONSES.employeesRetrieved,
      USER_ACTIONS.getEmployee,
      result.rows,
      meta
    );
  }

  async getOneEmployee(req: Request, res: Response) {
    const { orgId } = res.locals;
    const empId = req.params.employeeId;

    const employee = await this.employeeService.getEmployeeWithRelatedData(orgId, empId);

    return sendJsonResponse(res, RESPONSES.employeesRetrieved, USER_ACTIONS.getEmployee, employee);
  }

  async editOneEmployee(req: Request, res: Response) {
    const { orgId } = res.locals;
    const empId = req.params.employeeId;

    const payload = req.body as Partial<IEditEmployeeAttributes>;

    const updateResult = await this.employeeService.editOneEmployee(orgId, empId, payload);

    await sendOrganizationNotification(
      res,
      'Employee updated',
      `Employee with id: ${updateResult.updatedEmployeeDetails.id} details was updated`,
      'HIGH'
    );

    return sendJsonResponse(
      res,
      RESPONSES.employeeUpdated,
      USER_ACTIONS.editEmployee,
      updateResult
    );
  }

  async changeEmployeeEmploymentStatus(req: Request, res: Response) {
    const { orgId } = res.locals;
    const empId = req.params.employeeId;
    const newEmploymentStatus = String(req.query.employment_status).toLowerCase();

    const updatedEmployee = await this.employeeService.changeEmploymentStatus(
      orgId,
      empId,
      newEmploymentStatus
    );

    await sendOrganizationNotification(
      res,
      'Employee status changed',
      `${updatedEmployee.first_name} ${updatedEmployee.last_name} status was changed to ${newEmploymentStatus}`,
      'HIGH'
    );

    return sendJsonResponse(
      res,
      RESPONSES.employeeStatusChanged,
      USER_ACTIONS.editEmployeeStatus,
      updatedEmployee
    );
  }

  async searchForEmployees(req: Request, res: Response) {
    const { orgId } = res.locals;
    const { page, offset, limit } = getPagination(req);

    const searchParam = req.searchEmployeePayload;

    const employees = await this.employeeService.searchForEmployees(
      orgId,
      searchParam,
      offset,
      limit
    );
    const meta: Meta = { page, limit, count: employees.rows.length, totalCounts: employees.count };

    return sendJsonResponse(
      res,
      RESPONSES.employeesRetrieved,
      USER_ACTIONS.searchEmployee,
      employees.rows,
      meta
    );
  }

  async downloadMultipleEmployeeUploadTemplate(_req: Request, res: Response) {
    const buffer = await this.employeeService.downloadMultipleEmployeeUploadTemplate({
      content: [...CREATE_EMPLOYEE_TEMPLATE],
      workSheetName: 'create bulk employee template',
    });

    return sendFileResponse(res, USER_ACTIONS.downloadCreateBulkEmployeeTemplate, {
      filename: 'create-bulk-employee-template.xlsx',
      buffer,
    });
  }
}
