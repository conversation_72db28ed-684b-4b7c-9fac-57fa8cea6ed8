import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import EmployeeDeductibleServices from '../../../services/deductibles.service';
import { IEmployeeAttributes } from '../../../models/employee.model';
import { getPagination, sendOrganizationNotification } from '../../../utilities/global.utilities';
import { Meta } from '../../../interfaces/global.interfaces';
import { sendJsonResponse } from '../../../helpers/response.helpers';
import { RESPONSES } from '../../../constants/responses.constants';
import { USER_ACTIONS } from '../../../constants/values.constants';
import { IDeductibleAttributes } from '../../../models/deductible.model';
import { DEDUCTIBLES } from '../../../constants/notification.constants';

export default class OrgEmployeeDeductibleControllers extends RequestHandlerErrorWrapper {
  constructor(private deductibleService: EmployeeDeductibleServices) {
    super();
  }

  async getAllDeductibles(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const { page, offset, limit } = getPagination(req);

    const result = await this.deductibleService.getAllDeductibles(
      orgId,
      employee.id,
      offset,
      limit
    );

    const meta: Meta = {
      count: result.rows.length,
      page,
      limit,
      totalCounts: result.count,
    };

    return sendJsonResponse(
      res,
      RESPONSES.deductiblesRetrieved,
      USER_ACTIONS.getDeductible,
      result.rows,
      meta
    );
  }

  async getOneDeductible(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const deductibleId = req.params.deductibleId;

    const deductible = await this.deductibleService.getOneDeductible(
      orgId,
      employee.id,
      deductibleId
    );

    return sendJsonResponse(
      res,
      RESPONSES.deductiblesRetrieved,
      USER_ACTIONS.getDeductible,
      deductible
    );
  }

  async createMultipleDeductibles(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const payload = req.body as Partial<IDeductibleAttributes>[];

    // create multiple deductibles for the employee
    // note: deductible uniqueness validation should be implemented in the service layer
    const deductibles = await this.deductibleService.createOneDeductible(
      orgId,
      employee.id,
      payload
    );

    // send notification about deductible creation
    await sendOrganizationNotification(
      res,
      'deductibles created',
      `${req.user.email} has created ${deductibles.length} deductible(s) for employee`,
      'MEDIUM',
      DEDUCTIBLES
    );

    return sendJsonResponse(
      res,
      RESPONSES.deductiblesCreated,
      USER_ACTIONS.createDeductible,
      deductibles
    );
  }

  async editOneDeductible(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const deductibleId = req.params.deductibleId;
    const payload = req.body as Partial<IDeductibleAttributes>;

    // update specific deductible for the employee
    const deductible = await this.deductibleService.updateOneDeductible(
      orgId,
      employee.id,
      deductibleId,
      payload
    );

    // send notification about deductible update
    await sendOrganizationNotification(
      res,
      'deductible updated',
      `${req.user.email} has updated a deductible for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      DEDUCTIBLES
    );

    return sendJsonResponse(
      res,
      RESPONSES.deductiblesUpdated,
      USER_ACTIONS.editDeductible,
      deductible
    );
  }

  async deleteOneDeductible(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const deductibleId = req.params.deductibleId;

    // delete the deductible
    await this.deductibleService.deleteOneDeductible(orgId, employee.id, deductibleId);

    // send notification about deductible deletion
    await sendOrganizationNotification(
      res,
      'deductible deleted',
      `${req.user.email} has deleted a deductible for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      DEDUCTIBLES
    );

    return sendJsonResponse(res, RESPONSES.deductiblesDeleted, USER_ACTIONS.deleteDeductible);
  }
}
