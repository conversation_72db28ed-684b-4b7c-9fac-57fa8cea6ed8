import { instantiateRouter } from '../../../helpers/routes.helpers';
import {
  createDeductibleSchema,
  editDeductibleSchema,
} from '../../../middlewares/validators/schemas/request_body/deductible.schema';
import {
  validateRequestBody,
  validateRouteIdParams,
} from '../../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../../constants/route-params.constant';

const { router: specificEmpDeductiblesRouter, controller: EmployeeDeductibleController } =
  instantiateRouter('orgEmployeeDeductibleController');

const {
  createMultipleDeductibles: createDeductibles,
  getOneDeductible,
  editOneDeductible,
  getAllDeductibles,
} = EmployeeDeductibleController;

specificEmpDeductiblesRouter
  .route('/')
  .get(getAllDeductibles)
  .post(validateRequestBody(createDeductibleSchema), createDeductibles);

const deductibleId = ROUTE_IDS.deductibleId;

specificEmpDeductiblesRouter
  .route(`/:${deductibleId}`)
  .all(validateRouteIdParams(deductibleId))
  .get(getOneDeductible)
  .put(validateRequestBody(editDeductibleSchema), editOneDeductible);
// .delete(deductibleControllers.deleteDeductible);

export default specificEmpDeductiblesRouter;
