import { instantiateRouter } from '../../../helpers/routes.helpers';
import { createPaymentHistorySchema } from '../../../middlewares/validators/schemas/request_body/payment-history.schema';
import {
  validateRequestBody,
  validateRouteIdParams,
} from '../../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../../constants/route-params.constant';

const { router: specificEmpPaymentsRouter, controller: employeePaymentHistoryControllers } =
  instantiateRouter('orgEmployeePaymentController');

const { getOnePaymentHistory, createOnePaymentHistory, getAllPaymentHistories } =
  employeePaymentHistoryControllers;

specificEmpPaymentsRouter
  .route('/')
  .get(getAllPaymentHistories)
  .post(validateRequestBody(createPaymentHistorySchema), createOnePaymentHistory);

const paymentId = ROUTE_IDS.paymentId;

specificEmpPaymentsRouter.get(
  `/:${paymentId}`,
  validateRouteIdParams(paymentId),
  getOnePaymentHistory
);

export default specificEmpPaymentsRouter;
