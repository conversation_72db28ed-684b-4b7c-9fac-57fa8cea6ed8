import { isProductionEnv } from '../../../utilities/guards';
import { RateLimiters } from '../../../middlewares/utils/rate-limiter.middleware';
import middlewares from '../../../containers/middlewares.container';
import { instantiateRouter } from '../../../helpers/routes.helpers';
import {
  validateRequestBody,
  validateRouteIdParams,
  validateSearchEmployee,
} from '../../../middlewares/validators/global.validators';
import { createEmployeeSchema } from '../../../middlewares/validators/schemas/request_body/employee.schema';
import { ROUTE_IDS } from '../../../constants/route-params.constant';
import specificEmployeeRouter from './specific-employee.routes';

const { router: orgRouter, controller: employeeControllers } =
  instantiateRouter('orgEmployeeController');

if (isProductionEnv) {
  orgRouter.use(RateLimiters.organizationRequest);
}

const { authenticateUser, validateActiveSubscription } = middlewares.resolve('authMiddleware');

middlewares.resolve('utilityMiddleware');

orgRouter.use(authenticateUser, validateActiveSubscription);

const {
  searchForEmployees,
  downloadMultipleEmployeeUploadTemplate,
  createMultipleEmployees,
  getAllEmployees,
  createOneEmployee,
} = employeeControllers;

orgRouter.get('/search', validateSearchEmployee, searchForEmployees);

orgRouter.get('/bulk-upload/template', downloadMultipleEmployeeUploadTemplate);

orgRouter.post('/bulk-upload', validateRequestBody(createEmployeeSchema), createMultipleEmployees);

orgRouter
  .route('/')
  .post(validateRequestBody(createEmployeeSchema), createOneEmployee)
  .get(getAllEmployees);

orgRouter.use(
  `/:${ROUTE_IDS.employeeId}`,
  validateRouteIdParams(ROUTE_IDS.employeeId),
  specificEmployeeRouter
);

export default orgRouter;
