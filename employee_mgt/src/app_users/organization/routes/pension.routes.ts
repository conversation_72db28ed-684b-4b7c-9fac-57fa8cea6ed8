import { instantiateRouter } from '../../../helpers/routes.helpers';
import {
  createPensionSchema,
  editPensionSchema,
} from '../../../middlewares/validators/schemas/request_body/pension.schema';
import {
  validateRequestBody,
  validateRouteIdParams,
} from '../../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../../constants/route-params.constant';

const { router: specificEmpPensionsRouter, controller: employeePensionControllers } =
  instantiateRouter('orgEmployeePensionController');

const { createOnePension, getPension, editOnePension } = employeePensionControllers;

specificEmpPensionsRouter
  .route('/')
  .get(getPension)
  .post(validateRequestBody(createPensionSchema), createOnePension);

const pensionId = ROUTE_IDS.pensionId;

specificEmpPensionsRouter
  .route(`/:${pensionId}`)
  .all(validateRouteIdParams(pensionId))
  .put(validateRequestBody(editPensionSchema), editOnePension);
// .delete(validatePensionId, pensionControllers.DeletePension);

export default specificEmpPensionsRouter;
