import { instantiateRouter } from '../../../helpers/routes.helpers';
import { approveOrRejectLeaveQuerySchema } from '../../../middlewares/validators/schemas/query_params/leaves.query.params.schemas';
import {
  createLeaveSchema,
  editLeaveSchema,
} from '../../../middlewares/validators/schemas/request_body/leave.schema';
import {
  validateQueryParams,
  validateRequestBody,
  validateRouteIdParams,
} from '../../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../../constants/route-params.constant';

const { router: specificEmpLeavesRouter, controller: employeeLeaveControllers } = instantiateRouter(
  'orgEmployeeLeaveController'
);

const {
  getAllLeaves,
  approveOrRejectLeave,
  cancelLeave,
  getOneLeave,
  editOneLeave,
  createOneLeave,
} = employeeLeaveControllers;

specificEmpLeavesRouter
  .route('/')
  .get(getAllLeaves)
  .post(validateRequestBody(createLeaveSchema), createOneLeave);

const leaveId = ROUTE_IDS.leaveId;

// Leave approval or rejection
specificEmpLeavesRouter.patch(
  `/:${leaveId}/approve-or-reject`,
  validateRouteIdParams(leaveId),
  validateQueryParams(approveOrRejectLeaveQuerySchema),
  approveOrRejectLeave
);

// Leave cancel
specificEmpLeavesRouter.patch(`/:${leaveId}/cancel`, validateRouteIdParams(leaveId), cancelLeave);

// Leave details and update
specificEmpLeavesRouter
  .route(`/:${leaveId}`)
  .all(validateRouteIdParams(leaveId))
  .get(getOneLeave)
  .put(validateRequestBody(editLeaveSchema), editOneLeave);

export default specificEmpLeavesRouter;
