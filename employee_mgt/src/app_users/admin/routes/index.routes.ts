import middlewares from '../../../containers/middlewares.container';
import { RateLimiters } from '../../../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../../../utilities/guards';
import { instantiateRouter } from '../../../helpers/routes.helpers';
import {
  validateQueryParams,
  validateRouteIdParams,
  validateSearchEmployee,
} from '../../../middlewares/validators/global.validators';
import { organizationIdQuerySchema } from '../../../middlewares/validators/schemas/query_params/global.query.params.schema';
import { ROUTE_IDS } from '../../../constants/route-params.constant';

const { router: adminRouter, controller: adminEmployeeControllers } =
  instantiateRouter('adminEmployeeController');

if (isProductionEnv) {
  adminRouter.use(RateLimiters.adminRequest);
}

const Auth = middlewares.resolve('authMiddleware');

adminRouter.use(Auth.authenticateAdminUser);

adminRouter.use(
  validateQueryParams(organizationIdQuerySchema),
  middlewares.resolve('utilityMiddleware').extractOrgDetailsFromAdminRequest
);

adminRouter.get('/search', validateSearchEmployee, adminEmployeeControllers.searchForEmployees);

adminRouter.get(
  `/:${ROUTE_IDS.employeeId}`,
  validateRouteIdParams(ROUTE_IDS.employeeId),
  adminEmployeeControllers.getOneEmployee
);

adminRouter.get('/', adminEmployeeControllers.getAllEmployees);

export default adminRouter;
