import { Request, Response } from 'express';
import { getPagination } from '../../../utilities/global.utilities';
import { RESPONSES } from '../../../constants/responses.constants';
import { USER_ACTIONS } from '../../../constants/values.constants';
import { RequestHandlerErrorWrapper } from '../../../helpers/class.helpers';
import { getAdminAction } from './admin-controller.helpers';
import { Meta } from '../../../interfaces/global.interfaces';
import { sendJsonResponse } from '../../../helpers/response.helpers';
import EmployeeServices from '../../../services/employee.service';

export default class AdminEmployeeControllers extends RequestHandlerErrorWrapper {
  constructor(private employeeService: EmployeeServices) {
    super();
  }

  async getAllEmployees(req: Request, res: Response) {
    const orgId = res.locals.organizationId as string;
    const { page, offset, limit } = getPagination(req);

    const { rows: data, count } = await this.employeeService.getAllEmployees(orgId, offset, limit);

    const meta: Meta = {
      page,
      offset,
      limit,
      count: data.length,
      totalCounts: count,
    };

    return sendJsonResponse(
      res,
      RESPONSES.employeesRetrieved,
      getAdminAction(USER_ACTIONS.getEmployee),
      data,
      meta
    );
  }

  async getOneEmployee(req: Request, res: Response) {
    const { employeeId } = req.params;
    const orgId = res.locals.organizationId;

    const employee = await this.employeeService.getEmployeeWithRelatedData(orgId, employeeId);

    return sendJsonResponse(
      res,
      RESPONSES.employeesRetrieved,
      getAdminAction(USER_ACTIONS.getEmployee),
      employee
    );
  }

  async searchForEmployees(req: Request, res: Response) {
    const orgId = res.locals.organizationId as string;
    const { page, offset, limit } = getPagination(req);

    const searchParam = req.searchEmployeePayload;

    const { rows: data, count } = await this.employeeService.searchForEmployees(
      orgId,
      searchParam,
      offset,
      limit
    );

    const meta: Meta = { page, offset, limit, count: data.length, totalCounts: count };

    return sendJsonResponse(
      res,
      RESPONSES.employeesRetrieved,
      getAdminAction(USER_ACTIONS.searchEmployee),
      data,
      meta
    );
  }
}
